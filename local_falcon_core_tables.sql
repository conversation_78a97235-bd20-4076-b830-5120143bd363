-- =====================================================
-- Local Falcon Core Tables - Essential Tables Only
-- =====================================================
-- This script creates the 3 core tables needed for Local Falcon functionality
-- Run this script first to get the basic functionality working

-- =====================================================
-- Table: local_falcon_configurations
-- Purpose: Store Local Falcon scan configurations
-- =====================================================
CREATE TABLE IF NOT EXISTS `local_falcon_configurations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL COMMENT 'Configuration name',
  `keyword` varchar(255) NOT NULL COMMENT 'Search keyword',
  `business_name` varchar(255) NOT NULL COMMENT 'Business name to track',
  `place_id` varchar(255) DEFAULT NULL COMMENT 'Google Place ID',
  `center_lat` decimal(10,8) NOT NULL COMMENT 'Center latitude',
  `center_lng` decimal(11,8) NOT NULL COMMENT 'Center longitude',
  `grid_size` varchar(10) NOT NULL DEFAULT '5x5' COMMENT 'Grid size (e.g., 5x5)',
  `radius` decimal(8,2) NOT NULL COMMENT 'Search radius',
  `unit` enum('meters','kilometers','miles') NOT NULL DEFAULT 'kilometers' COMMENT 'Distance unit',
  `is_schedule_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Enable scheduled scans',
  `schedule_frequency` enum('daily','weekly','monthly') DEFAULT NULL COMMENT 'Schedule frequency',
  `alert_threshold` int(11) DEFAULT NULL COMMENT 'Alert threshold for ranking changes',
  `settings` json DEFAULT NULL COMMENT 'Additional configuration settings',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_keyword` (`keyword`),
  KEY `idx_business_name` (`business_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Local Falcon scan configurations';

-- =====================================================
-- Table: local_falcon_scan_results
-- Purpose: Store scan results and historical data
-- =====================================================
CREATE TABLE IF NOT EXISTS `local_falcon_scan_results` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `configuration_id` int(11) NOT NULL,
  `average_position` decimal(5,2) DEFAULT NULL COMMENT 'Average ranking position',
  `visibility_percentage` decimal(5,2) DEFAULT NULL COMMENT 'Visibility percentage',
  `total_searches` int(11) NOT NULL DEFAULT 0 COMMENT 'Total grid points searched',
  `found_in_results` int(11) NOT NULL DEFAULT 0 COMMENT 'Number of points where business was found',
  `rankings_data` json DEFAULT NULL COMMENT 'Detailed ranking data for each grid point',
  `scan_duration` int(11) DEFAULT NULL COMMENT 'Scan duration in seconds',
  `scan_status` enum('pending','running','completed','failed') NOT NULL DEFAULT 'pending',
  `error_message` text DEFAULT NULL COMMENT 'Error message if scan failed',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_configuration_id` (`configuration_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_scan_status` (`scan_status`),
  KEY `idx_avg_position` (`average_position`),
  KEY `idx_visibility` (`visibility_percentage`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Local Falcon scan results and historical data';

-- =====================================================
-- Table: local_falcon_alerts
-- Purpose: Store ranking alerts and notifications
-- =====================================================
CREATE TABLE IF NOT EXISTS `local_falcon_alerts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `configuration_id` int(11) NOT NULL,
  `alert_type` enum('ranking_drop','ranking_improvement','not_found','visibility_change') NOT NULL COMMENT 'Type of alert',
  `message` text NOT NULL COMMENT 'Alert message',
  `previous_position` decimal(5,2) DEFAULT NULL COMMENT 'Previous ranking position',
  `current_position` decimal(5,2) DEFAULT NULL COMMENT 'Current ranking position',
  `previous_visibility` decimal(5,2) DEFAULT NULL COMMENT 'Previous visibility percentage',
  `current_visibility` decimal(5,2) DEFAULT NULL COMMENT 'Current visibility percentage',
  `threshold_value` decimal(5,2) DEFAULT NULL COMMENT 'Threshold that triggered the alert',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether alert has been read',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL COMMENT 'When alert was marked as read',
  PRIMARY KEY (`id`),
  KEY `idx_configuration_id` (`configuration_id`),
  KEY `idx_alert_type` (`alert_type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_unread_alerts` (`is_read`, `created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Local Falcon ranking alerts and notifications';

-- =====================================================
-- Add Foreign Key Constraints (if users table exists)
-- =====================================================
-- Uncomment the following lines if you have a 'users' table and want foreign key constraints

/*
ALTER TABLE `local_falcon_configurations` 
ADD CONSTRAINT `fk_local_falcon_config_user` 
FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `local_falcon_scan_results` 
ADD CONSTRAINT `fk_local_falcon_scan_config` 
FOREIGN KEY (`configuration_id`) REFERENCES `local_falcon_configurations` (`id`) ON DELETE CASCADE;

ALTER TABLE `local_falcon_alerts` 
ADD CONSTRAINT `fk_local_falcon_alert_config` 
FOREIGN KEY (`configuration_id`) REFERENCES `local_falcon_configurations` (`id`) ON DELETE CASCADE;
*/

-- =====================================================
-- Sample Data for Testing (Optional)
-- =====================================================
-- Uncomment and modify the following to insert test data

/*
-- Sample configuration (replace user_id with actual user ID from your users table)
INSERT INTO `local_falcon_configurations` (
  `user_id`, `name`, `keyword`, `business_name`, `place_id`, 
  `center_lat`, `center_lng`, `grid_size`, `radius`, `unit`,
  `is_schedule_enabled`, `schedule_frequency`, `alert_threshold`
) VALUES (
  1, 'Downtown Restaurant Test', 'restaurant near me', 'Test Restaurant', 'ChIJN1t_tDeuEmsRUsoyG83frY4',
  40.7128, -74.0060, '5x5', 1.0, 'kilometers',
  0, NULL, 5
);

-- Sample scan result
INSERT INTO `local_falcon_scan_results` (
  `configuration_id`, `average_position`, `visibility_percentage`, 
  `total_searches`, `found_in_results`, `scan_status`, `scan_duration`
) VALUES (
  1, 8.5, 75.0, 25, 18, 'completed', 45
);

-- Sample alert
INSERT INTO `local_falcon_alerts` (
  `configuration_id`, `alert_type`, `message`, 
  `previous_position`, `current_position`, `is_read`
) VALUES (
  1, 'ranking_improvement', 'Great news! Your business ranking improved from position 12 to 8 for keyword "restaurant near me".',
  12.0, 8.0, 0
);
*/

-- =====================================================
-- Verification Queries
-- =====================================================
-- Run these queries after creating tables to verify everything is working

-- Check if tables were created successfully
-- SELECT TABLE_NAME, TABLE_COMMENT FROM INFORMATION_SCHEMA.TABLES 
-- WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME LIKE 'local_falcon_%';

-- Check table structures
-- DESCRIBE local_falcon_configurations;
-- DESCRIBE local_falcon_scan_results;
-- DESCRIBE local_falcon_alerts;

-- =====================================================
-- Usage Instructions
-- =====================================================
/*
To use this script:

1. Connect to your MySQL database
2. Run this script to create the tables
3. Uncomment and modify the foreign key constraints if you have a users table
4. Optionally uncomment and run the sample data for testing
5. Test the Local Falcon functionality in your application

The tables are designed to work with the Local Falcon API endpoints and will store:
- Configuration settings for each scan setup
- Historical scan results and performance data
- Alert notifications for ranking changes

Make sure your application has the proper database connection configured
and the Local Falcon models can access these tables.
*/
