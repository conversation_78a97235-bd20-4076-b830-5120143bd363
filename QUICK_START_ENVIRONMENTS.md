# Quick Start: Multiple Environments

## 🚀 Running Different Environments

### Development Environment
```bash
# Windows
npm run dev:win

# Unix/Linux/Mac  
npm run dev
```

### Staging Environment
```bash
# Windows
npm run staging:win

# Unix/Linux/Mac
npm run staging
```

### Production Environment
```bash
# Windows
npm run production:win

# Unix/Linux/Mac
npm run production
```

## 🔍 Validating Environment Configuration

```bash
# Validate development
npm run validate:env:dev

# Validate staging
npm run validate:env:staging

# Validate production
npm run validate:env:production
```

## 📁 Environment Files

- `.env.development` - Local development settings
- `.env.staging` - Staging environment settings  
- `.env.production` - Production environment settings

## ⚙️ Key Configuration Differences

| Setting | Development | Staging | Production |
|---------|-------------|---------|------------|
| **Environment** | DEVELOPMENT | STAGING | PRODUCTION |
| **Log Level** | INFO | INFO | ERROR |
| **Database** | gmb | gmb_staging | gmb_production |
| **S3 Bucket** | gmb-social-assets | gmb-social-assets-staging | gmb-social-assets-production |
| **Frontend URL** | http://localhost:5173 | https://staging-domain.com | https://production-domain.com |

## 🔧 Next Steps

1. **Update Staging Environment:**
   - Edit `.env.staging` with your actual staging credentials
   - Update database host, credentials, and S3 settings
   - Update social media app credentials for staging

2. **Update Production Environment:**
   - Edit `.env.production` with your actual production credentials
   - Use production database and S3 bucket
   - Use production social media app credentials
   - Ensure all URLs point to production domains

3. **Security:**
   - Never commit sensitive credentials to version control
   - Use different secrets for each environment
   - Rotate credentials regularly

## 🎯 Current Status

✅ Development environment - **Ready to use**  
⚠️ Staging environment - **Needs credential updates**  
⚠️ Production environment - **Needs credential updates**

For detailed setup instructions, see `ENVIRONMENT_SETUP.md`
