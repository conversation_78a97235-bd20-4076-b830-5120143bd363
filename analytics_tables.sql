-- Analytics Tables Migration
-- This migration creates tables to store Google My Business analytics data locally
-- to reduce API calls and improve performance

-- Table to store daily analytics metrics for each location
CREATE TABLE IF NOT EXISTS `location_analytics_daily` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `gmb_location_id` varchar(255) NOT NULL,
  `gmb_account_id` varchar(255) NOT NULL,
  `metric_date` date NOT NULL,
  `metric_type` varchar(100) NOT NULL,
  `metric_value` bigint(20) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_location_date_metric` (`gmb_location_id`, `metric_date`, `metric_type`),
  KEY `idx_location_date` (`gmb_location_id`, `metric_date`),
  KEY `idx_account_date` (`gmb_account_id`, `metric_date`),
  KEY `idx_metric_type` (`metric_type`),
  KEY `idx_date_range` (`metric_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table to store analytics sync log to track sync status and errors
CREATE TABLE IF NOT EXISTS `location_analytics_sync_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `gmb_location_id` varchar(255) NOT NULL,
  `gmb_account_id` varchar(255) NOT NULL,
  `sync_date` date NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `status` enum('pending', 'success', 'failed', 'partial') NOT NULL DEFAULT 'pending',
  `metrics_synced` int(11) DEFAULT 0,
  `error_message` text,
  `api_calls_made` int(11) DEFAULT 0,
  `sync_duration_seconds` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_location_sync_date` (`gmb_location_id`, `sync_date`),
  KEY `idx_status` (`status`),
  KEY `idx_sync_date` (`sync_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table to store analytics configuration and metadata
CREATE TABLE IF NOT EXISTS `location_analytics_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `gmb_location_id` varchar(255) NOT NULL,
  `gmb_account_id` varchar(255) NOT NULL,
  `sync_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `last_sync_date` date NULL,
  `sync_frequency_days` int(11) NOT NULL DEFAULT 1,
  `retention_days` int(11) NOT NULL DEFAULT 365,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_location_config` (`gmb_location_id`),
  KEY `idx_sync_enabled` (`sync_enabled`),
  KEY `idx_last_sync` (`last_sync_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default configuration for existing locations
INSERT IGNORE INTO `location_analytics_config` (
  `gmb_location_id`, 
  `gmb_account_id`, 
  `sync_enabled`, 
  `sync_frequency_days`, 
  `retention_days`
)
SELECT 
  `gmbLocationId`, 
  `gmbAccountId`, 
  1, 
  1, 
  365
FROM `gmb_locations` 
WHERE `statusId` = 1;
