const mysql = require('mysql2/promise');
const ReviewSettings = require('./models/reviewSettings.models');
require('dotenv').config({ path: '.env.development' });

async function testAIAutoReply() {
  console.log('🧪 Testing AI Auto Reply Implementation...');
  
  try {
    // Test 1: Database connection and column verification
    console.log('\n1. Testing database connection and schema...');
    
    const dbConfig = {
      host: process.env.APP_DB_HOST,
      user: process.env.APP_DB_USER,
      password: process.env.APP_DB_PASSWORD,
      database: process.env.APP_DB_NAME,
    };

    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connection successful');

    // Check if the new column exists
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'auto_reply_settings' 
      AND COLUMN_NAME = 'enable_ai_auto_reply'
    `, [dbConfig.database]);

    if (columns.length > 0) {
      console.log('✅ enable_ai_auto_reply column exists:', columns[0]);
    } else {
      console.log('❌ enable_ai_auto_reply column not found');
      return;
    }

    // Test 2: Test auto reply settings retrieval
    console.log('\n2. Testing auto reply settings retrieval...');
    
    const settings = await ReviewSettings.getAutoReplySettings(1); // Test with business ID 1
    console.log('✅ Auto reply settings retrieved:', {
      is_enabled: settings.is_enabled,
      enable_ai_auto_reply: settings.enable_ai_auto_reply,
      enabled_star_ratings: settings.enabled_star_ratings,
    });

    // Test 3: Test auto reply settings update with AI enabled
    console.log('\n3. Testing auto reply settings update with AI enabled...');
    
    const testSettings = {
      isEnabled: true,
      enabledStarRatings: [4, 5],
      delayMinutes: 0,
      onlyBusinessHours: false,
      enableAiAutoReply: true,
      businessHoursStart: "09:00:00",
      businessHoursEnd: "17:00:00",
    };

    await ReviewSettings.updateAutoReplySettings(1, testSettings);
    console.log('✅ Auto reply settings updated with AI enabled');

    // Verify the update
    const updatedSettings = await ReviewSettings.getAutoReplySettings(1);
    console.log('✅ Updated settings verified:', {
      is_enabled: updatedSettings.is_enabled,
      enable_ai_auto_reply: updatedSettings.enable_ai_auto_reply,
      enabled_star_ratings: updatedSettings.enabled_star_ratings,
    });

    // Test 4: Test AI reply generation
    console.log('\n4. Testing AI reply generation...');
    
    const Reviews = require('./models/reviews.models');
    const aiReply = await Reviews.getReplyFromAI(
      "Great service and friendly staff! Highly recommend.",
      5
    );
    console.log('✅ AI reply generated:', aiReply.substring(0, 100) + '...');

    await connection.end();
    console.log('\n🎉 All tests passed! AI Auto Reply implementation is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

// Run the test
testAIAutoReply();
