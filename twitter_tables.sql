-- Twitter Integration Database Tables
-- Run these SQL commands to set up Twitter integration tables

-- Create Twitter OAuth tokens table
CREATE TABLE IF NOT EXISTS twitter_oauth_tokens (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  twitter_user_id VARCHAR(255) NOT NULL,
  twitter_username VARCHAR(255) NOT NULL,
  twitter_user_name VARCHAR(255),
  twitter_user_email VARCHAR(255),
  twitter_user_picture TEXT,
  access_token TEXT NOT NULL,
  refresh_token TEXT,
  expires_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  status_id TINYINT DEFAULT 1,
  UNIQUE KEY unique_user_twitter (user_id, twitter_user_id),
  INDEX idx_user_id (user_id),
  INDEX idx_twitter_user_id (twitter_user_id),
  INDEX idx_status (status_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create Twitter accounts table (for organizations/business accounts)
CREATE TABLE IF NOT EXISTS twitter_accounts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  twitter_oauth_token_id INT NOT NULL,
  user_id INT NOT NULL,
  account_id VARCHAR(255) NOT NULL,
  account_name VARCHAR(255) NOT NULL,
  account_username VARCHAR(255) NOT NULL,
  account_description TEXT,
  account_picture_url TEXT,
  followers_count INT DEFAULT 0,
  following_count INT DEFAULT 0,
  tweet_count INT DEFAULT 0,
  is_verified TINYINT DEFAULT 0,
  is_active TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_user_account (user_id, account_id),
  INDEX idx_user_id (user_id),
  INDEX idx_account_id (account_id),
  INDEX idx_is_active (is_active),
  FOREIGN KEY (twitter_oauth_token_id) REFERENCES twitter_oauth_tokens(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create Twitter posts table
CREATE TABLE IF NOT EXISTS twitter_posts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  account_id VARCHAR(255) NOT NULL,
  twitter_post_id VARCHAR(255) NOT NULL,
  post_content JSON NOT NULL,
  post_response JSON,
  tweet_text TEXT,
  media_urls JSON,
  hashtags JSON,
  mentions JSON,
  reply_to_tweet_id VARCHAR(255),
  quote_tweet_id VARCHAR(255),
  published TINYINT DEFAULT 1,
  scheduled_publish_time TIMESTAMP NULL,
  status ENUM('draft', 'scheduled', 'published', 'failed') DEFAULT 'published',
  twitter_url TEXT,
  retweet_count INT DEFAULT 0,
  like_count INT DEFAULT 0,
  reply_count INT DEFAULT 0,
  quote_count INT DEFAULT 0,
  impression_count INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_twitter_post (twitter_post_id),
  INDEX idx_user_id (user_id),
  INDEX idx_account_id (account_id),
  INDEX idx_status (status),
  INDEX idx_published (published),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add Twitter-related columns to business table (optional)
-- Uncomment these lines if you want to track Twitter sync status at business level
/*
ALTER TABLE gmb_businesses_master 
ADD COLUMN twitter_sync_status TINYINT DEFAULT 0 COMMENT '0=Not Connected, 1=Connected, 2=Error',
ADD COLUMN twitter_account_id VARCHAR(255) NULL COMMENT 'Primary Twitter account ID for this business';

-- Add indexes for better performance
ALTER TABLE gmb_businesses_master ADD INDEX idx_twitter_sync_status (twitter_sync_status);
ALTER TABLE gmb_businesses_master ADD INDEX idx_twitter_account_id (twitter_account_id);
*/

-- Verify tables were created successfully
SHOW TABLES LIKE 'twitter_%';

-- Check table structures
DESCRIBE twitter_oauth_tokens;
DESCRIBE twitter_accounts;
DESCRIBE twitter_posts;
