const ReviewSettings = require("../models/reviewSettings.models");
const Reviews = require("../models/reviews.models");

const welcome = async (req, res) => {
  res.status(200).json({ message: "Review Settings API is working!" });
};

// Get all reply templates for a user
const getReplyTemplates = async (req, res) => {
  try {
    const { userId } = req.params;
    const { businessId, accountId, locationId } = req.query;

    const templates = await ReviewSettings.getReplyTemplates(
      userId,
      businessId,
      accountId,
      locationId
    );

    res.status(200).json({
      message: "Reply templates fetched successfully",
      data: templates,
    });
  } catch (error) {
    console.error("Error fetching reply templates:", error);
    res.status(500).json({
      message: "Failed to fetch reply templates",
      error: error.message,
    });
  }
};

// Create a new reply template
const createReplyTemplate = async (req, res) => {
  try {
    const { userId } = req.params;
    const { accountId, locationId } = req.query;
    const templateData = {
      ...req.body,
      userId: parseInt(userId),
    };

    console.log("=== CREATE TEMPLATE DEBUG ===");
    console.log("User ID:", userId);
    console.log("Account ID:", accountId);
    console.log("Location ID:", locationId);
    console.log("Template Data:", templateData);

    const result = await ReviewSettings.createReplyTemplate(
      templateData,
      accountId,
      locationId
    );

    console.log("Template creation result:", result);

    res.status(201).json({
      message: "Reply template created successfully",
      data: { id: result.insertId },
    });
  } catch (error) {
    console.error("Error creating reply template:", error);
    res.status(500).json({
      message: "Failed to create reply template",
      error: error.message,
    });
  }
};

// Update a reply template
const updateReplyTemplate = async (req, res) => {
  try {
    const { userId, templateId } = req.params;
    const templateData = req.body;

    const result = await ReviewSettings.updateReplyTemplate(
      parseInt(templateId),
      templateData,
      parseInt(userId)
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        message: "Template not found or you don't have permission to update it",
      });
    }

    res.status(200).json({
      message: "Reply template updated successfully",
    });
  } catch (error) {
    console.error("Error updating reply template:", error);
    res.status(500).json({
      message: "Failed to update reply template",
      error: error.message,
    });
  }
};

// Delete a reply template
const deleteReplyTemplate = async (req, res) => {
  try {
    const { userId, templateId } = req.params;

    const result = await ReviewSettings.deleteReplyTemplate(
      parseInt(templateId),
      parseInt(userId)
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        message: "Template not found or you don't have permission to delete it",
      });
    }

    res.status(200).json({
      message: "Reply template deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting reply template:", error);
    res.status(500).json({
      message: "Failed to delete reply template",
      error: error.message,
    });
  }
};

// Get auto-reply settings for a business/account/location
const getAutoReplySettings = async (req, res) => {
  try {
    const { businessId } = req.params;
    const { accountId, locationId } = req.query;

    console.log("=== GET AUTO-REPLY SETTINGS DEBUG ===");
    console.log("Business ID:", businessId);
    console.log("Account ID:", accountId);
    console.log("Location ID:", locationId);

    const settings = await ReviewSettings.getAutoReplySettings(
      parseInt(businessId),
      accountId,
      locationId
    );

    console.log("Retrieved settings:", settings);

    res.status(200).json({
      message: "Auto-reply settings fetched successfully",
      data: settings,
    });
  } catch (error) {
    console.error("Error fetching auto-reply settings:", error);
    res.status(500).json({
      message: "Failed to fetch auto-reply settings",
      error: error.message,
    });
  }
};

// Update auto-reply settings for a business/account/location
const updateAutoReplySettings = async (req, res) => {
  try {
    const { businessId } = req.params;
    const { accountId, locationId } = req.query;
    const settings = req.body;

    // Log the incoming data for debugging
    console.log("=== UPDATE AUTO-REPLY SETTINGS DEBUG ===");
    console.log("Business ID:", businessId);
    console.log("Account ID:", accountId);
    console.log("Location ID:", locationId);
    console.log("Received auto-reply settings:", settings);

    // Transform the data to match the model expectations
    const transformedSettings = {
      isEnabled: settings.is_enabled,
      enabledStarRatings: settings.enabled_star_ratings,
      delayMinutes: settings.delay_minutes,
      onlyBusinessHours: settings.only_business_hours,
      enableAiAutoReply: settings.enable_ai_auto_reply,
      businessHoursStart: settings.only_business_hours
        ? settings.business_hours_start
        : null,
      businessHoursEnd: settings.only_business_hours
        ? settings.business_hours_end
        : null,
    };

    console.log("Transformed settings:", transformedSettings);

    const result = await ReviewSettings.updateAutoReplySettings(
      parseInt(businessId),
      transformedSettings,
      accountId,
      locationId
    );

    res.status(200).json({
      message: "Auto-reply settings updated successfully",
    });
  } catch (error) {
    console.error("Error updating auto-reply settings:", error);
    res.status(500).json({
      message: "Failed to update auto-reply settings",
      error: error.message,
    });
  }
};

// Map template to businesses/accounts/locations
const mapTemplateToBusinesses = async (req, res) => {
  try {
    const { userId, templateId } = req.params;
    const { accountId, locationId } = req.query;
    const { businessIds } = req.body;

    console.log("=== MAP TEMPLATE TO BUSINESSES DEBUG ===");
    console.log("User ID:", userId);
    console.log("Template ID:", templateId);
    console.log("Account ID:", accountId);
    console.log("Location ID:", locationId);
    console.log("Business IDs:", businessIds);

    const result = await ReviewSettings.mapTemplateToBusinesses(
      parseInt(templateId),
      businessIds,
      parseInt(userId),
      accountId,
      locationId
    );

    console.log("Template mapping result:", result);

    res.status(200).json({
      message: "Template mapped to businesses successfully",
    });
  } catch (error) {
    console.error("Error mapping template to businesses:", error);
    res.status(500).json({
      message: "Failed to map template to businesses",
      error: error.message,
    });
  }
};

// Get template for auto-reply (used by auto-reply system)
const getTemplateForAutoReply = async (req, res) => {
  try {
    const { businessId, starRating } = req.params;

    const template = await ReviewSettings.getTemplateForAutoReply(
      parseInt(businessId),
      parseInt(starRating)
    );

    res.status(200).json({
      message: "Template fetched successfully",
      data: template,
    });
  } catch (error) {
    console.error("Error fetching template for auto-reply:", error);
    res.status(500).json({
      message: "Failed to fetch template for auto-reply",
      error: error.message,
    });
  }
};

// Generate AI text for reply templates
const generateTemplateTextWithAI = async (req, res) => {
  try {
    const { starRating } = req.body;

    if (!starRating || starRating < 1 || starRating > 5) {
      return res.status(400).json({
        message: "Valid star rating (1-5) is required",
      });
    }

    // Create a generic review comment based on star rating for AI to respond to
    let genericComment = "";

    switch (parseInt(starRating)) {
      case 5:
        genericComment = `Reply to 5 Star rating: Excellent service! Everything was perfect and exceeded my expectations.`;
        break;
      case 4:
        genericComment = `Reply to 4 Star rating: Great experience. Very satisfied with the service provided.`;
        break;
      case 3:
        genericComment = `Reply to 3 Star rating: Good service. It was okay, met my basic expectations.`;
        break;
      case 2:
        genericComment = `Reply to 2 Star rating: Service was below expectations. There were some issues that need improvement.`;
        break;
      case 1:
        genericComment = `Reply to 1 Star rating: Very disappointed with the service. Multiple problems and poor experience.`;
        break;
    }

    // Use the existing AI function to generate a reply
    const aiGeneratedText = await Reviews.getReplyFromAI(
      genericComment,
      parseInt(starRating)
    );

    res.status(200).json({
      message: "AI template text generated successfully",
      data: {
        generatedText: aiGeneratedText,
        starRating: parseInt(starRating),
        genericComment: genericComment,
      },
    });
  } catch (error) {
    console.error("Error generating AI template text:", error);
    res.status(500).json({
      message: "Failed to generate AI template text",
      error: error.message,
    });
  }
};

module.exports = {
  welcome,
  getReplyTemplates,
  createReplyTemplate,
  updateReplyTemplate,
  deleteReplyTemplate,
  getAutoReplySettings,
  updateAutoReplySettings,
  mapTemplateToBusinesses,
  getTemplateForAutoReply,
  generateTemplateTextWithAI,
};
