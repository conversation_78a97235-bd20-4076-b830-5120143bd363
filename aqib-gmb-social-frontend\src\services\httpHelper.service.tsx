/*This is an Example of Calling Other Class Function in React Native*/
import { Component, Dispatch } from "react";
import axios, { HttpStatusCode } from "axios";
import { AxiosRequestConfig } from "axios";
import { ILoginModel } from "../interfaces/request/ILoginModel";
import { AUTH_UNAUTHORIZED } from "../constants/reducer.constant";

class HttpHelperService {
  dispatch: Dispatch<any> = () => {
    throw new Error("Dispatch function must be overridden by a provider.");
  };

  config: AxiosRequestConfig = {
    headers: {
      "Content-Type": "application/json",
      Accept: "*/*",
    },
    data: {},
  };

  processEndpoint = `${import.meta.env.VITE_BASE_URL}`;

  constructor(dispatch: Dispatch<any>) {
    this.dispatch = dispatch;
  }

  checkIfUnAuthorized = (error: any) => {
    const errorReponse = error.response;

    if (errorReponse && errorReponse.status == HttpStatusCode.Unauthorized) {
      console.log("[Axios Error]  Session Expired", error.response);
      this.dispatch({
        type: AUTH_UNAUTHORIZED,
        payload: { isUnAuthorised: true },
      });
    }
  };

  /**
   *
   * @param url
   * @returns
   */
  get = async (url: string, headers: any = null) => {
    try {
      const endpoint = `${this.processEndpoint}/${url}`;
      this.appendAuthorization(headers);
      this.removeFormHeader();
      console.log(
        `[API REQUEST] -> [GET] -> [ENDPOINT] -> ${endpoint} -> \n [HEADERS] -> ${this.config.headers}`
      );
      const response = await axios.get(endpoint, this.config);
      console.log(
        `[API REQUEST] -> [GET] -> [ENDPOINT] -> ${endpoint} \n [RESPONSE] -> ${JSON.stringify(
          response.data
        )}`
      );
      return response.data;
    } catch (e: any) {
      console.log("[Axios Error]  ", e);
      this.checkIfUnAuthorized(e);
      throw e;
    } finally {
    }
  };

  login = async (loginRequest: ILoginModel) => {
    try {
      const endpoint = `${this.processEndpoint}/auth/login`;

      // let config = {
      //   method: "post",
      //   maxBodyLength: Infinity,
      //   url: endpoint,
      //   headers: {
      //     "Content-Type": "multipart/form-data;",
      //   },
      //   data: formData,
      // };

      const response = await axios.post(endpoint, loginRequest, this.config);
      return response.data;
    } catch (e) {
      console.log("[Axios Error]  ", e);
      this.checkIfUnAuthorized(e);
      throw e;
    }
  };

  /**
   *
   * @param url
   * @param requestBody
   * @returns
   */
  post = async (url: string, requestBody: any, headers: any = null) => {
    try {
      const endpoint = `${this.processEndpoint}/${url}`;
      this.appendAuthorization(headers);
      this.removeFormHeader();
      console.log(
        `[API REQUEST] -> [POST] -> ${endpoint} \n [BODY] -> ${JSON.stringify(
          requestBody
        )} -> \n [HEADERS] -> ${this.config.headers}`
      );
      const response = await axios.post(endpoint, requestBody, this.config);
      console.log(
        `[API REQUEST] -> [POST] -> ${endpoint} \n [BODY] -> ${JSON.stringify(
          requestBody
        )} \n [RESPONSE] -> ${JSON.stringify(response.data)}`
      );
      return response.data;
    } catch (e) {
      console.log("[Axios Error]  ", e);
      this.checkIfUnAuthorized(e);
      throw e;
    }
  };

  /**
   *
   * @param urlSS
   * @param requestBody
   * @returns
   */
  postFormData = async (url: string, requestBody: FormData) => {
    try {
      const endpoint = `${this.processEndpoint}/${url}`;
      this.appendAuthorization();
      this.appendFormHeader();
      console.log(
        `[API REQUEST] -> [POST] -> ${endpoint} \n [BODY] -> ${JSON.stringify(
          requestBody
        )} -> \n [HEADERS] -> ${this.config.headers}`
      );
      const response = await axios.post(endpoint, requestBody, this.config);
      console.log(
        `[API REQUEST] -> [POST] -> ${endpoint} \n [BODY] -> ${JSON.stringify(
          requestBody
        )} \n [RESPONSE] -> ${JSON.stringify(response.data)}`
      );
      return response.data;
    } catch (e) {
      console.log("[Axios Error]  ", e);
      this.checkIfUnAuthorized(e);
      throw e;
    }
  };

  /**
   *
   * @param url
   * @param requestBody
   * @returns
   */
  delete = async (url: string) => {
    try {
      const endpoint = `${this.processEndpoint}/${url}`;
      this.appendAuthorization();
      this.removeFormHeader();
      console.log(
        `[API REQUEST] -> [DELETE] -> [ENDPOINT] -> ${endpoint} -> \n [HEADERS] -> ${this.config.headers}`
      );
      const response = await axios.delete(endpoint, this.config);
      console.log(
        `[API REQUEST] -> [DELETE] -> [ENDPOINT] -> ${endpoint} \n [RESPONSE] -> ${JSON.stringify(
          response.data
        )}`
      );
      return response.data;
    } catch (e) {
      console.log("[Axios Error]  ", e);
      this.checkIfUnAuthorized(e);
      throw e;
    }
  };

  /**
   *
   * @param url
   * @param requestBody
   * @returns
   */
  put = async (url: string, requestBody: any) => {
    try {
      const endpoint = `${this.processEndpoint}/${url}`;
      this.appendAuthorization();
      this.removeFormHeader();
      console.log(
        `[API REQUEST] -> [PUT] -> ${endpoint} \n [BODY] -> ${JSON.stringify(
          requestBody
        )} -> \n [HEADERS] -> ${this.config.headers}`
      );
      const response = await axios.put(endpoint, requestBody, this.config);
      console.log(
        `[API REQUEST] -> [PUT] -> ${endpoint} \n [BODY] -> ${JSON.stringify(
          requestBody
        )} \n [RESPONSE] -> ${JSON.stringify(response.data)}`
      );
      return response.data;
    } catch (e) {
      console.log("[Axios Error]  ", e);
      this.checkIfUnAuthorized(e);
      throw e;
    }
  };

  /**
   *
   * @param url
   * @param requestBody
   * @returns
   */
  downloadFile = async (url: string) => {
    try {
      const endpoint = `${this.processEndpoint}/${url}`;
      this.appendAuthorization();
      this.removeFormHeader();
      return await axios.get(endpoint, {
        ...this.config,
        responseType: "blob",
      });
      // .then((response) => {
      //   // create file link in browser's memory
      //   const href = URL.createObjectURL(response.data);
      //   // create "a" HTML element with href to file & click
      //   const link = document.createElement("a");
      //   link.href = href;
      //   link.setAttribute("download", "file.pdf"); //or any other extension
      //   document.body.appendChild(link);
      //   link.click();

      //   // clean up "a" element & remove ObjectURL
      //   document.body.removeChild(link);
      //   URL.revokeObjectURL(href);
      // });
    } catch (e) {
      console.log("[Axios Error]  ", e);
      throw e;
    }
  };

  appendAuthorization = async (headers: any = null) => {
    var loggedInUser = localStorage.getItem("MyLocoBiz_UserInfo");
    if (loggedInUser) {
      this.config.headers = {
        ...this.config.headers,
        Authorization: `Bearer ${JSON.parse(loggedInUser).token}`,
        "authentication-token": `${JSON.parse(loggedInUser).token}`,
      };

      if (headers) {
        for (const [key, value] of Object.entries(headers)) {
          console.log(`${key}: ${value}`);
          switch (key) {
            case "x-gmb-location-id":
              this.config.headers = {
                ...this.config.headers,
                "x-gmb-location-id": value as string,
              };
              break;
            case "x-gmb-question-id":
              this.config.headers = {
                ...this.config.headers,
                "x-gmb-question-id": value as string,
              };
              break;
            case "x-gmb-account-id":
              this.config.headers = {
                ...this.config.headers,
                "x-gmb-account-id": value as string,
              };
              break;
            case "x-gmb-business-id":
              this.config.headers = {
                ...this.config.headers,
                "x-gmb-business-id": value as string,
              };
              break;
            case "x-gmb-review-id":
              this.config.headers = {
                ...this.config.headers,
                "x-gmb-review-id": value as string,
              };
              break;
          }
        }
      }
    }
  };

  appendLoginHeader = async () => {
    this.config.headers = {
      "Content-Type": "application/x-www-form-urlencoded",
    };
  };

  appendFormHeader = async () => {
    this.config.headers = {
      ...this.config.headers,
      "Content-Type": "multipart/form-data",
    };
  };

  removeFormHeader = async () => {
    const { headers } = this.config;

    if (headers && headers["Content-Type"] === "multipart/form-data") {
      delete headers["Content-Type"];
    }

    this.config.headers = {
      ...headers,
    };
  };
}

export default HttpHelperService;
