# Local Falcon Integration

A comprehensive local search ranking tracking system integrated into the GMB Social platform. Track your business rankings across geographic grid points, monitor competitors, and analyze local search performance with heat map visualizations.

## 🚀 Features

### Core Functionality

- **📍 Geographic Grid Analysis** - Create custom grids around target locations
- **🔍 Real-time Ranking Checks** - Track business positions using Google Places API
- **🗺️ Heat Map Visualization** - Color-coded ranking performance across grid points
- **📊 Performance Dashboard** - Comprehensive analytics and trend analysis
- **🚨 Smart Alerts** - Automated notifications for ranking changes
- **👥 Competitor Tracking** - Monitor competitor positions and performance
- **📈 Historical Trends** - Track ranking improvements over time
- **📋 Data Export** - Export reports in PDF, CSV, and Excel formats

### Advanced Features

- **⚡ Batch Processing** - Efficient bulk ranking checks
- **🔄 Automated Scheduling** - Set up recurring ranking checks
- **🎯 Custom Search Terms** - Track multiple keywords per location
- **📱 Responsive Design** - Works on desktop and mobile devices
- **🔐 User Authentication** - Secure access to your data
- **⚙️ Configurable Alerts** - Customizable thresholds and notification methods

## 📋 Table of Contents

- [Installation](#installation)
- [Configuration](#configuration)
- [Database Setup](#database-setup)
- [API Endpoints](#api-endpoints)
- [Frontend Usage](#frontend-usage)
- [Architecture](#architecture)
- [Development](#development)
- [Troubleshooting](#troubleshooting)

## 🛠️ Installation

### Prerequisites

- Node.js 14+ and npm
- MySQL 5.7+ or 8.0+
- Google Places API key
- Existing GMB Social platform setup

### Backend Setup

1. **Install Dependencies** (if not already installed)

   ```bash
   cd aqib-gmb-social-backend
   npm install
   ```

2. **Configure Environment Variables**
   Add to your `.env.development` file:

   ```bash
   # Google Places API Configuration
   GOOGLE_PLACES_API_KEY=your-google-places-api-key-here
   LOCAL_FALCON_DEFAULT_RADIUS=5000
   LOCAL_FALCON_MAX_RESULTS=25
   LOCAL_FALCON_RATE_LIMIT_DELAY=100
   ```

3. **Set Up Database Tables**

   ```bash
   npm run setup:local-falcon
   ```

4. **Start Backend Server**
   ```bash
   npm start
   # or for development
   npm run dev
   ```

### Frontend Setup

The frontend components are already integrated into the existing GMB Social frontend. No additional setup required.

## ⚙️ Configuration

### Google Places API Setup

1. **Get API Key**

   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create/select a project
   - Enable **Places API** and **Places API (New)**
   - Create API credentials
   - Restrict API key to your server IPs

2. **Configure Rate Limits**
   ```bash
   LOCAL_FALCON_RATE_LIMIT_DELAY=100  # milliseconds between requests
   LOCAL_FALCON_MAX_RESULTS=25        # max results per search
   LOCAL_FALCON_DEFAULT_RADIUS=5000   # default search radius in meters
   ```

### Database Configuration

The system uses your existing MySQL database. Tables are created automatically with the setup script.

## 🗄️ Database Setup

### Automatic Setup

```bash
npm run setup:local-falcon
```

### Manual Setup

If you prefer manual setup, run the SQL file:

```bash
mysql -u username -p database_name < local_falcon_tables.sql
```

### Database Tables Created

- `local_rankings` - Store ranking data for grid points
- `competitor_businesses` - Manage competitor tracking
- `ranking_alerts` - Alert management system
- `ranking_schedules` - Automated ranking check schedules
- `ranking_export_logs` - Track data export operations
- `local_falcon_config` - Local Falcon configuration per grid

## 🔌 API Endpoints

### Ranking Operations

```http
POST   /local-falcon/check-rankings       # Manual ranking checks
GET    /local-falcon/grid-rankings/:id    # Get ranking data
GET    /local-falcon/ranking-history/:id  # Get historical data
```

### Competitor Management

```http
POST   /local-falcon/competitors          # Add competitor
GET    /local-falcon/competitors/:userId  # Get user competitors
DELETE /local-falcon/competitors/:id      # Delete competitor
```

### Alert Management

```http
POST   /local-falcon/alerts               # Create alert
GET    /local-falcon/alerts/:userId       # Get user alerts
PUT    /local-falcon/alerts/:id           # Update alert
DELETE /local-falcon/alerts/:id           # Delete alert
```

### Schedule Management

```http
POST   /local-falcon/schedule-checks      # Schedule automated checks
GET    /local-falcon/schedules/:userId    # Get user schedules
```

### API Documentation

```http
GET    /local-falcon/                     # API welcome & documentation
```

## 🖥️ Frontend Usage

### Accessing Local Falcon

1. Navigate to **Local Falcon** page in GMB Social
2. You'll see the Local Falcon interface with search and scan functionality

### Step-by-Step Usage

#### 1. Search for Location

```
1. Enter location (e.g., "New York, NY")
2. Select business from search results
3. Configure scan parameters
4. Run Local Falcon scan
5. View ranking results
```

#### 2. Configure Tracking (Local Falcon Tab)

```
1. Add search terms: "restaurant near me", "best pizza"
2. Add businesses to track: Your business names/IDs
3. Add competitors: Competitor business names
4. Set tracking interval: Hourly, daily, weekly
5. Configure alert thresholds
```

#### 3. Check Rankings

```
1. Click "Check Rankings" button
2. System checks all grid points × search terms × businesses
3. Results displayed immediately
4. Data saved for historical analysis
```

#### 4. View Heat Map (Heat Map Tab)

```
1. Toggle "Show Ranking Heat Map"
2. Color-coded visualization:
   🟢 Green: Top 3 positions
   🟡 Yellow: Top 10 positions
   🟠 Orange: Top 20 positions
   🔴 Red: Below 20
   ⚫ Gray: Not visible
```

#### 5. Analyze Performance (Dashboard Tab)

```
- Key metrics: Total checks, visibility rate, average position
- Trend analysis: Historical performance comparison
- Competitor analysis: Performance vs competitors
- Export reports: PDF, CSV, Excel formats
```

#### 6. Manage Alerts (Alerts Tab)

```
- Create alerts: Position drop, visibility loss, competitor gain
- Set thresholds: Number of positions before alerting
- Notification methods: Email, SMS, webhook
- Toggle alerts: Active/inactive management
```

#### 7. Track Competitors (Competitors Tab)

```
- Add competitors: Business names and IDs
- Performance analytics: Average position, visibility rate
- Search & filter: Find specific competitors
- Activity tracking: Last seen dates
```

## 🏗️ Architecture

### Backend Components

```
├── controllers/
│   └── localFalcon.controller.js    # API endpoints
├── models/
│   └── localFalcon.models.js        # Database operations
├── services/
│   └── localSearchService.js        # Google Places API integration
├── routes/
│   └── localFalcon.js               # Route definitions
└── database/
    └── setup_local_falcon.js        # Database setup script
```

### Frontend Components

```
├── components/localFalcon/
│   ├── LocalFalconMap.component.tsx         # Map visualization
│   ├── LocalFalconControls.component.tsx    # Search and scan controls
│   └── LocalFalconResults.component.tsx     # Results display
└── services/localFalcon/
    └── localFalcon.service.tsx               # API service calls
```

### Data Flow

```
Frontend → API Endpoints → Models → Database
                ↓
        LocalSearchService → Google Places API
```

## 🔧 Development

### Running Tests

```bash
# Test database setup
npm run test:local-falcon-db

# Test API endpoints (when implemented)
npm test
```

### Development Mode

```bash
# Backend with hot reload
npm run dev

# Frontend development server
cd aqib-gmb-social-frontend
npm run dev
```

### Adding New Features

1. **Backend**: Add endpoints in `localFalcon.controller.js`
2. **Database**: Update models in `localFalcon.models.js`
3. **Frontend**: Add components in `components/localFalcon/`
4. **API Integration**: Update `localFalcon.service.tsx`

## 🐛 Troubleshooting

### Common Issues

#### Database Setup Fails

```bash
# Check MySQL connection
mysql -u username -p

# Verify environment variables
cat .env.development | grep DB

# Run setup with verbose logging
npm run setup:local-falcon
```

#### Google Places API Errors

```bash
# Check API key configuration
echo $GOOGLE_PLACES_API_KEY

# Verify API is enabled in Google Cloud Console
# Check API quotas and billing
```

#### Frontend Not Loading

```bash
# Check backend is running
curl http://localhost:3000/local-falcon/

# Check frontend service calls
# Open browser dev tools → Network tab
```

### Error Messages

| Error                              | Solution                                      |
| ---------------------------------- | --------------------------------------------- |
| `Specified key was too long`       | Run updated database setup script             |
| `Google Places API not configured` | Add valid API key to .env.development         |
| `Grid configuration not found`     | Create geo grid first in Grid Setup tab       |
| `No grid points found`             | Generate grid points before checking rankings |

### Performance Optimization

1. **API Rate Limiting**: Adjust `LOCAL_FALCON_RATE_LIMIT_DELAY`
2. **Database Indexing**: Tables include optimized indexes
3. **Batch Processing**: Use batch ranking checks for efficiency
4. **Caching**: Consider implementing Redis for frequent queries

## 📊 Usage Examples

### Restaurant Chain Analysis

```javascript
// Example configuration for a restaurant chain
{
  location: "Chicago, IL",
  gridSize: "5x5",
  radius: 10000,
  searchTerms: [
    "italian restaurant",
    "pizza near me",
    "fine dining chicago"
  ],
  businesses: ["Mario's Italian Bistro"],
  competitors: [
    "Tony's Pizza",
    "Bella Vista Restaurant",
    "Chicago Deep Dish Co"
  ]
}
```

### Multi-Location Business

```javascript
// Track multiple locations
{
  locations: [
    "New York, NY",
    "Los Angeles, CA",
    "Chicago, IL"
  ],
  searchTerms: ["coffee shop", "best coffee"],
  checkInterval: "daily"
}
```

## 📈 Metrics & Analytics

### Key Performance Indicators

- **Average Position**: Mean ranking across all checks
- **Visibility Rate**: Percentage of searches where business appears
- **Top 3 Rate**: Percentage of top 3 rankings
- **Competitor Gap**: Position difference vs competitors
- **Geographic Performance**: Heat map analysis

### Trend Analysis

- **Position Changes**: Track ranking improvements/declines
- **Seasonal Patterns**: Identify time-based trends
- **Competitor Movements**: Monitor competitive landscape
- **Search Term Performance**: Best/worst performing keywords

## 🔒 Security

### API Security

- All endpoints require authentication
- Users can only access their own data
- Input validation on all parameters
- Rate limiting to prevent abuse

### Data Privacy

- Ranking data is user-specific
- Competitor data is anonymized
- No sharing of business information
- Secure API key storage

## 📞 Support

### Documentation

- API documentation: `GET /local-falcon/`
- Frontend components: Inline JSDoc comments
- Database schema: `local_falcon_tables.sql`

### Getting Help

1. Check this README for common issues
2. Review error logs in browser dev tools
3. Check backend logs for API errors
4. Verify Google Places API quotas

---

## 🎉 Success!

Your Local Falcon integration is now ready! Start by creating your first geo grid and running a ranking check to see the system in action.

**Happy ranking tracking!** 🚀
