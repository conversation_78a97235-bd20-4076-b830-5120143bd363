// Simple test to check S3 URL generation
const axios = require('axios');

async function testS3URLs() {
  try {
    console.log('Testing S3 URL generation...');
    
    // Test the backend API
    const response = await axios.get('http://localhost:3000/v1/manage-assets/business/45?page=1&limit=5', {
      headers: {
        'Authorization': 'Bearer your-token-here' // You'll need to replace this
      }
    });
    
    console.log('API Response:', response.data);
    
    if (response.data.success && response.data.data.assets.length > 0) {
      const asset = response.data.data.assets[0];
      console.log('\nFirst asset details:');
      console.log('- ID:', asset.id);
      console.log('- S3 Key:', asset.s3_key);
      console.log('- S3 URL:', asset.s3_url);
      console.log('- Thumbnail URL:', asset.thumbnail_s3_url);
      
      // Test if the URL is accessible
      console.log('\nTesting URL accessibility...');
      try {
        const urlTest = await axios.head(asset.s3_url);
        console.log('✓ S3 URL is accessible:', urlTest.status);
      } catch (urlError) {
        console.log('✗ S3 URL failed:', urlError.response?.status, urlError.message);
      }
      
      if (asset.thumbnail_s3_url) {
        try {
          const thumbTest = await axios.head(asset.thumbnail_s3_url);
          console.log('✓ Thumbnail URL is accessible:', thumbTest.status);
        } catch (thumbError) {
          console.log('✗ Thumbnail URL failed:', thumbError.response?.status, thumbError.message);
        }
      }
    }
    
  } catch (error) {
    console.error('Test failed:', error.response?.data || error.message);
  }
}

testS3URLs();
