## Branching strategy

### main

`main` branch will have the production-ready code.

### develop

`develop` branch will have non-breaking code of feature development code.

### feature/{feature-name}

`feature/{feature-name}` branch will have specific feature working code. will be merged back to `develop` once it's fully ready for testing.

### bugFix/{bugfix-name}

`bugfix/{feature-name}` branch will have bug specific code. will be merged back to `develop` once it's fully ready for testing.


Use case:

-----------------------------------------------------

Login feature:

git checkout develop.
git pull origin develop.
git checkout -b feature/login
git push origin feature/login

PR to be raised to develop

feature/login -> develop

`develop` branch code will be deployed to dev environment

-----------------------------------------------------

For production deployment:

PR to be raised to main

develop -> main

`main` branch code will be deployed to dev environment

------------------------------------------------------

Bug Fixing:

git checkout develop.
git pull origin develop.
git checkout -b bugfix/login-token-issue
git push origin bugfix/login-token-issue

PR to be raised to develop

bugfix/login-token-issue -> develop

`develop` branch code will be deployed to dev environment

## Code Commit Rules

Whenever you are pushing some code to github it should have proper commit message.

Eg:

git commit -m "feat:taskNo#module-task completed"

git commit -m "feat:2#authentiation-Login feature"

feat: feature
rf: refactor
bugFix: Bug Fix

## Task creation

1. Make sure to create the task before start working on any feature development.
[https://docs.google.com/spreadsheets/d/1f0cqNmcTW8IuPrHdZ2iusviJ5p-YH14mdf0NyGVrUq8/edit?usp=sharing]

2. Front-end task name should have prefix [FE]
    Back-end task name should have prefix [BE]
