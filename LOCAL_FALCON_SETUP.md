# Local Falcon API Integration Setup

This document explains how to set up and configure the Local Falcon API integration in the backend.

## Overview

The Local Falcon integration provides location-based business search and ranking analysis functionality. The integration is implemented in the backend for security and proper API key management.

## Environment Configuration

### Required Environment Variables

Add the following environment variable to your backend configuration:

```bash
# Local Falcon API Configuration
LOCAL_FALCON_API_KEY=fd6c0112fd7080b7179121e664c28789
```

### Setting Up Environment Variables

1. **For Development:**

   - Create a `.env` file in the backend root directory
   - Add the LOCAL_FALCON_API_KEY variable

2. **For Production:**
   - Set the environment variable in your deployment environment
   - Ensure the API key is kept secure and not exposed in logs

## API Endpoints

The backend provides the following Local Falcon endpoints:

### 1. Location Suggestions

```
GET /local-falcon/location-suggestions?query={search_term}&near={location}
```

**Parameters:**

- `query` (required): Search term for location suggestions
- `near` (optional): Near location for context

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "place_id": "ChIJvT1tK9m3xokRZqyragH9EdM",
      "name": "PA Home Supply",
      "address": "300 E Allegheny Ave, Philadelphia, PA 19134",
      "lat": 39.997790699999996,
      "lng": -75.12491729999999,
      "rating": "4.2",
      "reviews": 128,
      "sab": false,
      "map_link": "https://www.google.com/maps/place/..."
    }
  ],
  "source": "local_falcon"
}
```

### 2. Business Search

```
POST /local-falcon/places
```

**Request Body:**

```json
{
  "query": "home supply",
  "lat": 40.1372968,
  "lng": -74.8152315,
  "radius": 5,
  "unit": "kilometers",
  "near": "Levittown, PA"
}
```

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "name": "PA Home Supply",
      "placeId": "ChIJvT1tK9m3xokRZqyragH9EdM",
      "address": "300 E Allegheny Ave, Philadelphia, PA 19134",
      "rating": 4.2,
      "totalRatings": 128,
      "lat": 39.997790699999996,
      "lng": -75.12491729999999,
      "isServiceAreaBusiness": false,
      "map_link": "https://www.google.com/maps/place/..."
    }
  ],
  "source": "local_falcon"
}
```

## Local Falcon API Details

### API Endpoint

```
https://api.localfalcon.com/v1/places/
```

### Request Format

The Local Falcon API uses form-data format:

```bash
curl --location 'https://api.localfalcon.com/v1/places/' \
--form 'api_key="fd6c0112fd7080b7179121e664c28789"' \
--form 'query="home supply"' \
--form 'near="Levittown, PA"'
```

### Response Format

```json
{
  "code": 200,
  "code_desc": false,
  "success": true,
  "message": false,
  "parameters": {
    "query": "home supply",
    "near": "Levittown, PA"
  },
  "data": {
    "count": 20,
    "suggestions": [
      {
        "place_id": "ChIJvT1tK9m3xokRZqyragH9EdM",
        "lat": 39.997790699999996,
        "lng": -75.12491729999999,
        "name": "PA Home Supply",
        "address": "300 E Allegheny Ave, Philadelphia, PA 19134",
        "sab": false,
        "map_link": "https://www.google.com/maps/place/...",
        "rating": "4.2",
        "reviews": 128
      }
    ],
    "ttr": 3
  }
}
```

## Implementation Details

### Backend Controller

- **File:** `controllers/localFalcon.controller.js`
- **Functions:**
  - `getLocationSuggestions()` - Handles location autocomplete
  - `searchPlaces()` - Handles business search

### Data Transformation

The backend transforms Local Falcon API responses to match our frontend interface:

```javascript
// Local Falcon format -> Our format
{
  place_id: suggestion.place_id,
  name: suggestion.name,
  address: suggestion.address,
  formatted_address: suggestion.address,
  lat: suggestion.lat,
  lng: suggestion.lng,
  rating: suggestion.rating,
  reviews: suggestion.reviews,
  vicinity: suggestion.address,
  description: suggestion.name,
  sab: suggestion.sab,
  map_link: suggestion.map_link,
}
```

### Error Handling

- API failures return proper error responses (no fallback to mock data)
- Timeout set to 10 seconds
- Comprehensive logging for debugging

### Security Features

- API key stored in environment variables
- No direct frontend access to Local Falcon API
- Request validation and sanitization

## Frontend Integration

### Dual Search System: Google Places + Local Falcon

The Local Falcon Controls component now features two separate search sections:

#### 1. Location Search (Google Places API)

- **Purpose**: Get precise latitude and longitude coordinates for any location worldwide
- **Flow**: User types location → Google Places suggestions → Select location → Get coordinates
- **Usage**: Sets the center coordinates for Local Falcon searches

#### 2. Place Search (Local Falcon API)

- **Purpose**: Search for businesses and places using Local Falcon's database
- **Flow**: User types business/place → Local Falcon suggestions → Select place → Trigger search
- **Usage**: Find specific businesses or places near the selected coordinates

```typescript
// Google Places location flow (for coordinates)
handleGoogleLocationSuggestions -> googlePlacesService.getServiceAreaSuggestions(query)
handleGoogleLocationSelect -> googlePlacesService.getPlaceDetails(placeId) -> update coordinates

// Local Falcon place flow (for business search)
handleFalconLocationSuggestions -> localFalconService.searchLocationSuggestions(query)
handleFalconLocationSelect -> onSearch(LocalFalconSearchRequest)
```

### Service Integration

The frontend service (`localFalcon.service.tsx`) calls the backend endpoints:

```typescript
// Business search with location coordinates
searchPlaces = async (searchRequest: LocalFalconSearchRequest) => {
  return await this._httpHelperService.post(
    "/local-falcon/places",
    searchRequest // includes query, lat, lng, radius, unit, near
  );
};
```

## Testing

### Test Location Suggestions

```bash
curl -X GET "http://localhost:3000/v1/local-falcon/location-suggestions?query=New%20York" \
  -H "authentication-token: YOUR_JWT_TOKEN"
```

### Test Business Search

```bash
curl -X POST "http://localhost:3000/v1/local-falcon/places" \
  -H "Content-Type: application/json" \
  -H "authentication-token: YOUR_JWT_TOKEN" \
  -d '{
    "query": "restaurant",
    "lat": 40.7128,
    "lng": -74.0060,
    "near": "New York, NY"
  }'
```

## Troubleshooting

### Common Issues

1. **API Key Invalid**

   - Verify the LOCAL_FALCON_API_KEY environment variable
   - Check if the API key is active and has sufficient quota

2. **Network Timeouts**

   - Check internet connectivity
   - Verify Local Falcon API status

3. **Fallback Data**
   - If you see `"source": "fallback"` in responses, the Local Falcon API call failed
   - Check logs for specific error messages

### Logs

Monitor the application logs for Local Falcon API calls:

```bash
# Success logs
"Local Falcon API response received"

# Error logs
"Local Falcon API call failed, using fallback"
```

## Production Considerations

1. **Rate Limiting:** Monitor API usage to stay within Local Falcon limits
2. **Caching:** Consider implementing response caching for frequently requested data
3. **Monitoring:** Set up alerts for API failures
4. **Backup:** Ensure fallback data provides reasonable user experience

## Dependencies

The integration requires the following npm packages:

- `axios` - HTTP client for API calls
- `form-data` - For Local Falcon's form-data format
- `dotenv` - Environment variable management (if used)

## Support

For Local Falcon API issues:

- Documentation: https://www.localfalcon.com/api/docs
- Support: Contact Local Falcon support team

For implementation issues:

- Check application logs
- Verify environment configuration
- Test with provided curl examples
