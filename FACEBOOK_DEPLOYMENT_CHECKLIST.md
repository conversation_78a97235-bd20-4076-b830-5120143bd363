# Facebook Integration Deployment Checklist

## Pre-Deployment Setup

### ✅ Database Configuration
- [x] Database credentials configured in `.env.development`
- [ ] Test database connection: `npm run test:facebook-db`
- [ ] Setup Facebook tables: `npm run setup:facebook`
- [ ] Verify tables created successfully

### ✅ Facebook App Configuration
- [ ] Create Facebook Developer App at https://developers.facebook.com/
- [ ] Configure OAuth redirect URIs
- [ ] Request required permissions:
  - [ ] `pages_manage_posts`
  - [ ] `pages_read_engagement`
  - [ ] `pages_show_list`
  - [ ] `public_profile`
- [ ] Add Facebook credentials to `.env.development`:
  ```env
  FACEBOOK_CLIENT_ID=your_facebook_app_id
  FACEBOOK_CLIENT_SECRET=your_facebook_app_secret
  FACEBOOK_REDIRECT_URI=http://localhost:5000/v1/facebook/callback
  FRONTEND_URL=http://localhost:3000
  ```

### ✅ Code Deployment
- [x] Frontend components implemented
- [x] Backend services implemented
- [x] Database models created
- [x] API routes configured
- [x] Error handling implemented

## Testing Checklist

### ✅ Database Testing
- [ ] Run: `npm run test:facebook-db`
- [ ] Verify all Facebook tables exist
- [ ] Check table structures match schema
- [ ] Verify indexes are created

### ✅ Backend API Testing
- [ ] Start backend: `npm run devStart`
- [ ] Test authentication endpoint: `POST /v1/facebook/authenticate`
- [ ] Test pages endpoint: `GET /v1/facebook/pages/:userId/:businessId`
- [ ] Test post creation endpoint: `POST /v1/facebook/post/:userId/:businessId`

### ✅ Frontend Integration Testing
- [ ] Start frontend application
- [ ] Navigate to Create Social Post screen
- [ ] Click Facebook tab
- [ ] Verify Facebook login component appears
- [ ] Test Facebook authentication flow
- [ ] Test page selection dropdown
- [ ] Test post creation form
- [ ] Test image upload functionality
- [ ] Test post scheduling

### ✅ End-to-End Testing
- [ ] Complete Facebook OAuth flow
- [ ] Select Facebook page
- [ ] Create text-only post
- [ ] Create post with image
- [ ] Create scheduled post
- [ ] Verify posts appear on Facebook
- [ ] Check database records are created
- [ ] Test error scenarios

## Production Deployment

### ✅ Environment Configuration
- [ ] Update Facebook app redirect URIs for production
- [ ] Configure production environment variables
- [ ] Update FRONTEND_URL for production domain
- [ ] Verify SSL certificates for HTTPS

### ✅ Database Migration
- [ ] Backup production database
- [ ] Run Facebook integration migration on production
- [ ] Verify tables created successfully
- [ ] Test database connectivity

### ✅ Application Deployment
- [ ] Deploy backend changes
- [ ] Deploy frontend changes
- [ ] Restart application services
- [ ] Verify application starts successfully

### ✅ Post-Deployment Verification
- [ ] Test Facebook authentication in production
- [ ] Create test Facebook post
- [ ] Monitor application logs
- [ ] Check error rates and performance
- [ ] Verify Facebook API integration works

## Monitoring & Maintenance

### ✅ Logging
- [ ] Monitor Facebook API error logs
- [ ] Track authentication success/failure rates
- [ ] Monitor post creation metrics
- [ ] Set up alerts for critical errors

### ✅ Performance
- [ ] Monitor database query performance
- [ ] Track API response times
- [ ] Monitor S3 upload performance
- [ ] Check memory and CPU usage

### ✅ Security
- [ ] Verify access tokens are stored securely
- [ ] Monitor for unauthorized access attempts
- [ ] Check OAuth flow security
- [ ] Verify API rate limiting

## Troubleshooting Guide

### Common Issues
1. **Database Connection Failed**
   - Check `.env.development` credentials
   - Verify database server accessibility
   - Test with: `npm run test:facebook-db`

2. **Facebook Authentication Failed**
   - Verify Facebook app credentials
   - Check redirect URI configuration
   - Ensure required permissions are approved

3. **Post Creation Failed**
   - Check Facebook page permissions
   - Verify access token validity
   - Review Facebook API error responses

4. **Image Upload Failed**
   - Check S3 configuration
   - Verify image format and size
   - Review S3 upload logs

### Support Contacts
- Development Team: [contact information]
- Facebook Developer Support: https://developers.facebook.com/support/
- Database Administrator: [contact information]

## Rollback Plan

### If Issues Occur
1. **Database Rollback**
   ```sql
   DROP TABLE IF EXISTS facebook_posts;
   DROP TABLE IF EXISTS facebook_pages;
   DROP TABLE IF EXISTS facebook_oauth_tokens;
   ALTER TABLE gmb_businesses_master 
   DROP COLUMN facebook_sync_status,
   DROP COLUMN facebook_page_id;
   ```

2. **Code Rollback**
   - Revert to previous application version
   - Remove Facebook routes from main router
   - Hide Facebook tab in frontend

3. **Configuration Rollback**
   - Remove Facebook environment variables
   - Disable Facebook app in developer console

## Success Criteria

### ✅ Deployment Successful When:
- [ ] All database tables created successfully
- [ ] Facebook authentication flow works end-to-end
- [ ] Users can create and publish Facebook posts
- [ ] Image uploads work correctly
- [ ] Post scheduling functions properly
- [ ] Error handling works as expected
- [ ] No critical errors in logs
- [ ] Performance meets requirements

---

**Deployment Date:** ___________  
**Deployed By:** ___________  
**Verified By:** ___________  
**Status:** ___________
