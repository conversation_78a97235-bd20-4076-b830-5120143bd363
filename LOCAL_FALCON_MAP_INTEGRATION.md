# Local Falcon Map Integration

## Overview

The Local Falcon Map integration provides a visual representation of local search ranking data on an interactive Google Maps interface. This enhancement allows users to see their business rankings across different geographic grid points with color-coded markers and detailed information windows.

## Features

### 1. Enhanced Map Visualization
- **Interactive Google Maps**: Full-featured map with zoom, pan, and different map types
- **Color-coded Markers**: Visual ranking indicators with the following color scheme:
  - 🟢 Green: #1 ranking
  - 🟢 Light Green: Top 3 rankings
  - 🟡 Yellow: Top 5 rankings  
  - 🟠 Orange: Top 10 rankings
  - 🔴 Red: Not found or low ranking

### 2. Ranking Markers
- **Numbered Markers**: Display actual ranking position on the marker
- **Enhanced Info Windows**: Detailed information including:
  - Grid position (row, column)
  - GPS coordinates
  - Ranking position with color coding
  - Business name and details
  - Distance from center point

### 3. Heat Map Visualization
- **Toggle Heat Map**: Switch between marker and heat map views
- **Weighted Visualization**: Better rankings show higher intensity
- **Custom Gradient**: Color gradient optimized for ranking data

### 4. Real Data Processing
- **Raw API Integration**: Processes actual Local Falcon API responses
- **Grid Point Generation**: Automatically creates grid points from scan data
- **Business Matching**: Matches target business across grid points

## Configuration

### Environment Variables
Update your `.env.development` file:

```env
# Google Maps API Key (required for map functionality)
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Base API URL
VITE_BASE_URL=http://localhost:3000/v1
```

### Google Maps API Setup
1. Enable the following APIs in Google Cloud Console:
   - Maps JavaScript API
   - Places API
   - Maps Static API (optional)

2. Add the following libraries to your API key restrictions:
   - `places`
   - `visualization`

## Usage

### Loading Sample Data
1. Navigate to the Local Falcon screen
2. Click "Load Sample Scan Data (Demo)" button
3. The map will automatically populate with sample ranking data
4. Switch to "Analytics Dashboard" tab to see processed results

### Map Controls
- **Heat Map Toggle**: Switch between markers and heat map visualization
- **Map Type Controls**: Change between roadmap, satellite, hybrid views
- **Zoom Controls**: Zoom in/out for detailed view
- **Street View**: Access street-level imagery

### Marker Interactions
- **Click Markers**: View detailed ranking information
- **Hover Effects**: See quick ranking summary
- **Color Coding**: Instantly identify performance areas

## Data Structure

### Scan Result Processing
The map component processes raw Local Falcon API responses:

```typescript
interface LocalFalconScanResult {
  keyword: string;
  businessName: string;
  gridConfiguration: LocalFalconGridRequest;
  gridPoints: LocalFalconGridPoint[];
  rankings: LocalFalconRankingResult[];
  averagePosition: number;
  visibilityPercentage: number;
  totalSearches: number;
  foundInResults: number;
  rawResponse?: any; // Raw Local Falcon API data
}
```

### Grid Point Structure
```typescript
interface LocalFalconGridPoint {
  lat: number;
  lng: number;
  gridPosition: {
    row: number;
    col: number;
  };
}
```

### Ranking Result Structure
```typescript
interface LocalFalconRankingResult {
  position: number;
  business: LocalFalconBusiness;
  distance: number;
  gridPoint: LocalFalconGridPoint;
  searchResults: LocalFalconBusiness[];
}
```

## Performance Metrics Display

The map shows key performance indicators:
- **Average Ranking Position**: Overall performance across grid
- **Visibility Percentage**: Percentage of grid points where business appears
- **Found/Total Ratio**: Number of successful searches vs total searches

## Troubleshooting

### Common Issues

1. **Map Not Loading**
   - Check Google Maps API key configuration
   - Verify API key has required permissions
   - Check browser console for errors

2. **No Markers Appearing**
   - Ensure scan data contains valid coordinates
   - Check that rawResponse data is properly formatted
   - Verify grid points are being generated

3. **Heat Map Not Working**
   - Confirm visualization library is loaded
   - Check that ranking data exists
   - Verify heat map toggle is enabled

### Debug Mode
Enable debug logging by adding to browser console:
```javascript
localStorage.setItem('debug', 'localfalcon:*');
```

## Future Enhancements

### Planned Features
- **Competitor Overlay**: Show competitor rankings on same map
- **Historical Comparison**: Compare rankings over time
- **Custom Grid Sizes**: Support for different grid configurations
- **Export Functionality**: Save map views as images
- **Mobile Optimization**: Enhanced mobile map experience

### API Integrations
- **Real-time Updates**: Live ranking monitoring
- **Batch Processing**: Handle multiple keyword scans
- **Alert System**: Notifications for ranking changes

## Technical Notes

### Performance Considerations
- Map renders efficiently with up to 25 grid points (5x5 grid)
- Heat map uses weighted visualization for better performance
- Markers are clustered for large datasets

### Browser Compatibility
- Modern browsers with ES6+ support
- Google Maps JavaScript API v3
- WebGL support recommended for heat maps

### Security
- API keys should be restricted to specific domains
- Environment variables properly configured
- No sensitive data exposed in client-side code
