# Twitter Integration

This document provides a comprehensive guide for the Twitter integration implementation following the same patterns as Google and Facebook integrations.

## Overview

The Twitter integration allows users to:
- Connect their Twitter accounts via OAuth 2.0
- Create and publish tweets
- Upload media (images and videos)
- Schedule tweets for later
- Manage multiple Twitter accounts
- Track tweet performance metrics

## Architecture

### Backend Components

- **TwitterService**: Core Twitter API integration (`services/twitter.service.js`)
- **TwitterController**: Request handling and business logic (`controllers/twitter.controller.js`)
- **TwitterModels**: Database operations for Twitter data (`models/twitter.models.js`)
- **TwitterRoutes**: API endpoint definitions (`routes/twitter.js`)

### Database Tables

- **twitter_oauth_tokens**: Stores user OAuth tokens
- **twitter_accounts**: Stores connected Twitter accounts
- **twitter_posts**: Stores created posts and metadata

### Frontend Components

- **TwitterPostForm**: Tweet creation form (`components/twitterPostForm.component.tsx`)
- **TwitterPostPreview**: Tweet preview component (`components/twitter/TwitterPostPreview.tsx`)
- **TwitterPostStatusDialog**: Post creation status tracking (`components/TwitterPostStatusDialog.tsx`)
- **TwitterMultiAccountSelector**: Account selection component (`components/twitter/TwitterMultiAccountSelector.tsx`)
- **TwitterLogin**: Authentication component (`components/twitter/TwitterLogin.tsx`)
- **TwitterService**: Frontend API service (`services/twitter/twitter.service.ts`)

## Setup Instructions

### 1. Twitter App Configuration

1. Go to [Twitter Developer Portal](https://developer.twitter.com/)
2. Create a new app or use existing one
3. Configure OAuth 2.0 settings:
   - **App permissions**: Read and Write
   - **Type of App**: Web App
   - **Callback URLs**: `http://localhost:3000/v1/twitter/callback`
   - **Website URL**: `http://localhost:3000`
4. Note down your **Client ID** and **Client Secret**

### 2. Environment Configuration

Add the following to your `.env.development` file:

```env
# Twitter Integration
TWITTER_CLIENT_ID=your_twitter_client_id
TWITTER_CLIENT_SECRET=your_twitter_client_secret
TWITTER_REDIRECT_URI=http://localhost:3000/v1/twitter/callback
TWITTER_API_VERSION=v2
```

### 3. Database Setup

The database configuration is already available in `.env.development`.

#### Quick Setup

```bash
# Run the SQL script to create Twitter tables
mysql -h your_host -u your_user -p your_database < database/twitter_tables.sql
```

#### Manual Setup

Execute the SQL commands in `database/twitter_tables.sql` or refer to `TWITTER_DATABASE_SETUP_GUIDE.md` for detailed instructions.

### 4. Backend Integration

The Twitter integration is already integrated into the main application:

- Routes are registered in `routes.js`
- Controllers handle authentication and post creation
- Models manage database operations
- Services handle Twitter API communication

### 5. Frontend Integration

The Twitter integration is integrated into the main create social post screen:

- Tab 4 is designated for Twitter
- Form, preview, and status components are implemented
- Authentication flow is handled via popup
- Multi-account selection is supported

## API Endpoints

### Authentication
- `POST /v1/twitter/authenticate` - Initiate Twitter OAuth
- `POST /v1/twitter/callback` - Handle OAuth callback
- `POST /v1/twitter/callback-validation` - Validate callback (AJAX)

### Account Management
- `GET /v1/twitter/accounts/:userId/:businessId` - Get user's Twitter accounts

### Post Management
- `POST /v1/twitter/post/:userId/:businessId` - Create Twitter post
- `GET /v1/twitter/posts/:userId/:businessId` - Get user's Twitter posts

## Usage

### 1. Connect Twitter Account

1. Navigate to Create Social Post page
2. Click on Twitter tab
3. Click "Connect" button
4. Complete OAuth flow in popup window
5. Account will be connected and available for posting

### 2. Create Tweet

1. Select Twitter tab
2. Choose Twitter account(s)
3. Enter tweet text (280 character limit)
4. Optionally upload media
5. Optionally schedule for later
6. Click "Post Tweet" or "Schedule Tweet"

### 3. Multi-Account Posting

1. Select multiple Twitter accounts
2. Create tweet content
3. Submit - tweets will be posted to all selected accounts
4. Track status in the status dialog

## Features

### Tweet Creation
- 280 character limit with real-time counter
- Hashtag and mention detection
- Media upload support (images and videos)
- Scheduling support
- Multi-account posting

### Media Support
- Images: JPG, PNG, GIF, WebP
- Videos: MP4, AVI, MOV, WMV, FLV, WebM
- Automatic upload to S3
- Preview in tweet preview

### Account Management
- Multiple account support
- Account verification status display
- Follower/following count display
- Account profile picture display

### Status Tracking
- Real-time post creation progress
- Individual account status tracking
- Error handling and display
- Success/failure notifications

## Error Handling

The integration includes comprehensive error handling:

- OAuth authentication errors
- API rate limiting
- Network connectivity issues
- Invalid media formats
- Character limit violations
- Account permission issues

## Security

- OAuth 2.0 with PKCE for secure authentication
- Access tokens stored securely in database
- State parameter validation for CSRF protection
- Input validation and sanitization
- Rate limiting compliance

## Testing

### Backend Testing
```bash
# Test database connection
npm run test:twitter-db

# Test Twitter authentication
curl -X POST http://localhost:3000/v1/twitter/authenticate \
  -H "Content-Type: application/json" \
  -d '{"userId": 1, "businessId": 1}'
```

### Frontend Testing
1. Start the application: `npm run devStart`
2. Navigate to Create Social Post page
3. Test Twitter tab functionality
4. Verify authentication flow
5. Test tweet creation and posting

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify Twitter app credentials
   - Check redirect URI configuration
   - Ensure required permissions are granted

2. **Database Connection Failed**
   - Check `.env.development` credentials
   - Verify database server accessibility
   - Run database setup script

3. **Post Creation Failed**
   - Check Twitter account permissions
   - Verify access token validity
   - Review Twitter API error responses

4. **Media Upload Failed**
   - Check S3 configuration
   - Verify media format and size
   - Review S3 upload logs

### Support

- Twitter Developer Documentation: https://developer.twitter.com/en/docs
- Twitter API v2 Reference: https://developer.twitter.com/en/docs/api-reference-index

## Next Steps

After successful setup:

1. Configure Twitter app credentials
2. Test the integration thoroughly
3. Monitor API usage and rate limits
4. Set up error monitoring and logging
5. Consider implementing webhook for real-time updates

## Rollback

If you need to remove the Twitter integration:

```sql
DROP TABLE IF EXISTS twitter_posts;
DROP TABLE IF EXISTS twitter_accounts;
DROP TABLE IF EXISTS twitter_oauth_tokens;
```

Remove Twitter environment variables and restart the application.
