const axios = require("axios");
const logger = require("../utils/logger");

class BackendApiService {
  constructor() {
    this.baseURL = process.env.BACKEND_API_URL || "http://localhost:3000";
    this.timeout = parseInt(process.env.BACKEND_API_TIMEOUT) || 30000;
    this.serviceUserId = process.env.SERVICE_USER_ID || "52";
    this.serviceAuthToken = process.env.SERVICE_AUTH_TOKEN;

    // Create axios instance with default config
    this.apiClient = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        "Content-Type": "application/json",
        "User-Agent": "GMB-AutoReply-Service/1.0",
      },
    });

    // Add request interceptor for logging
    this.apiClient.interceptors.request.use(
      (config) => {
        logger.logAutoReply("API_REQUEST", {
          method: config.method?.toUpperCase(),
          url: config.url,
          headers: this.sanitizeHeaders(config.headers),
        });
        return config;
      },
      (error) => {
        logger.error("API request error:", error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging
    this.apiClient.interceptors.response.use(
      (response) => {
        logger.logAutoReply("API_RESPONSE", {
          status: response.status,
          url: response.config.url,
          success: true,
        });
        return response;
      },
      (error) => {
        logger.error("API response error:", {
          status: error.response?.status,
          url: error.config?.url,
          message: error.message,
          data: error.response?.data,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Sanitize headers for logging (remove sensitive data)
   */
  sanitizeHeaders(headers) {
    const sanitized = { ...headers };
    if (sanitized.Authorization) {
      sanitized.Authorization = "Bearer ***";
    }
    if (sanitized["x-auth-token"]) {
      sanitized["x-auth-token"] = "***";
    }
    return sanitized;
  }

  /**
   * Get authentication headers for API calls
   * This is a placeholder - you'll need to implement proper authentication
   */
  getAuthHeaders() {
    const headers = {};

    if (this.serviceAuthToken) {
      headers["Authorization"] = `Bearer ${this.serviceAuthToken}`;
    }

    // Alternative: If using a different auth method, implement here
    headers["authentication-token"] = this.serviceAuthToken;

    return headers;
  }

  /**
   * Reply to a Google My Business review
   * @param {Object} reviewData - Review data containing accountId, locationId, reviewId, etc.
   * @param {string} comment - Reply comment text
   * @returns {Promise<Object>} API response
   */
  async replyToReview(reviewData, comment) {
    try {
      const { gmbAccountId, gmbLocationId, reviewId } = reviewData;

      logger.logAutoReply("REVIEW_REPLY_START", {
        reviewId,
        accountId: gmbAccountId,
        locationId: gmbLocationId,
        commentLength: comment.length,
      });

      const requestData = {
        comment: comment,
        userId: parseInt(this.serviceUserId),
      };

      const headers = {
        ...this.getAuthHeaders(),
        "x-gmb-account-id": gmbAccountId,
        "x-gmb-location-id": gmbLocationId,
        "x-gmb-review-id": reviewId,
      };

      const response = await this.apiClient.post(
        "/v1/gmb-reviews/reviews-comment",
        requestData,
        { headers }
      );

      logger.logAutoReply("REVIEW_REPLY_SUCCESS", {
        reviewId,
        status: response.status,
        message: response.data?.message,
      });

      return {
        success: true,
        status: response.status,
        data: response.data,
      };
    } catch (error) {
      logger.error("Error replying to review:", {
        reviewId: reviewData.reviewId,
        error: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });

      return {
        success: false,
        status: error.response?.status || 500,
        error: error.message,
        data: error.response?.data,
      };
    }
  }

  /**
   * Generate AI reply for a review
   * @param {string} comment - Review comment
   * @param {number} rating - Star rating
   * @returns {Promise<Object>} API response with generated text
   */
  async generateAIReply(comment, rating) {
    try {
      logger.logAutoReply("AI_REPLY_API_START", {
        comment: comment?.substring(0, 100) + "...",
        rating,
      });

      const response = await this.apiClient.post(
        "/api/review-settings/generate-template-text",
        {
          starRating: rating,
          genericComment: comment,
        }
      );

      if (response.data && response.data.data) {
        logger.logAutoReply("AI_REPLY_API_SUCCESS", {
          rating,
          responseLength: response.data.data.generatedText?.length || 0,
        });

        return {
          success: true,
          data: response.data.data,
        };
      } else {
        logger.logAutoReply("AI_REPLY_API_NO_DATA", {
          rating,
          response: response.data,
        });

        return {
          success: false,
          error: "No generated text in response",
        };
      }
    } catch (error) {
      logger.error("AI reply generation failed:", {
        rating,
        error: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });

      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Test API connectivity
   * @returns {Promise<boolean>} True if API is reachable
   */
  async testConnection() {
    try {
      // Try multiple endpoints to test connectivity
      const endpoints = ["/v1/health", "/v1/gmb-reviews/", "/"];

      for (const endpoint of endpoints) {
        try {
          const response = await this.apiClient.get(endpoint);
          if (response.status === 200) {
            logger.logAutoReply("API_HEALTH_CHECK_SUCCESS", {
              endpoint,
              status: response.status,
            });
            return true;
          }
        } catch (endpointError) {
          // Continue to next endpoint
          continue;
        }
      }

      return false;
    } catch (error) {
      logger.error("Backend API connection test failed:", error.message);
      return false;
    }
  }

  /**
   * Get service status
   * @returns {Object} Service configuration and status
   */
  getStatus() {
    return {
      baseURL: this.baseURL,
      timeout: this.timeout,
      serviceUserId: this.serviceUserId,
      hasAuthToken: !!this.serviceAuthToken,
      isConfigured: !!(this.baseURL && this.serviceUserId),
    };
  }
}

module.exports = new BackendApiService();
