# Twitter Integration Database Setup Guide

This guide provides multiple methods to set up the Twitter integration database tables, compatible with different MySQL versions.

## Database Configuration

The database configuration is already available in `.env.development`:

```env
APP_DB_HOST=aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com
APP_DB_USER=admin
APP_DB_PASSWORD=lAverOpERiaN
APP_DB_NAME=gmb
```

## Setup Methods

### Method 1: Automated Setup (Recommended)

This method uses Node.js scripts to safely create tables and add columns:

```bash
# Step 1: Test database connection
npm run test:twitter-db

# Step 2: Create Twitter tables
npm run setup:twitter

# Step 3: Verify installation
npm run verify:twitter
```

### Method 2: Manual SQL Setup

If the automated setup fails, use these SQL commands directly:

```sql
-- Create Twitter OAuth tokens table
CREATE TABLE IF NOT EXISTS twitter_oauth_tokens (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  business_id INT NOT NULL,
  twitter_user_id VARCHAR(255) NOT NULL,
  twitter_username VARCHAR(255) NOT NULL,
  twitter_user_name VARCHAR(255),
  twitter_user_email VARCHAR(255),
  twitter_user_picture TEXT,
  access_token TEXT NOT NULL,
  refresh_token TEXT,
  expires_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  status_id TINYINT DEFAULT 1,
  UNIQUE KEY unique_user_business_twitter (user_id, business_id, twitter_user_id),
  INDEX idx_user_id (user_id),
  INDEX idx_business_id (business_id),
  INDEX idx_twitter_user_id (twitter_user_id),
  INDEX idx_status (status_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create Twitter accounts table (for organizations/business accounts)
CREATE TABLE IF NOT EXISTS twitter_accounts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  twitter_oauth_token_id INT NOT NULL,
  user_id INT NOT NULL,
  business_id INT NOT NULL,
  account_id VARCHAR(255) NOT NULL,
  account_name VARCHAR(255) NOT NULL,
  account_username VARCHAR(255) NOT NULL,
  account_description TEXT,
  account_picture_url TEXT,
  followers_count INT DEFAULT 0,
  following_count INT DEFAULT 0,
  tweet_count INT DEFAULT 0,
  is_verified TINYINT DEFAULT 0,
  is_active TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_user_business_account (user_id, business_id, account_id),
  INDEX idx_user_id (user_id),
  INDEX idx_business_id (business_id),
  INDEX idx_account_id (account_id),
  INDEX idx_is_active (is_active),
  FOREIGN KEY (twitter_oauth_token_id) REFERENCES twitter_oauth_tokens(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create Twitter posts table
CREATE TABLE IF NOT EXISTS twitter_posts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  business_id INT NOT NULL,
  account_id VARCHAR(255) NOT NULL,
  twitter_post_id VARCHAR(255) NOT NULL,
  post_content JSON NOT NULL,
  post_response JSON,
  tweet_text TEXT,
  media_urls JSON,
  hashtags JSON,
  mentions JSON,
  reply_to_tweet_id VARCHAR(255),
  quote_tweet_id VARCHAR(255),
  published TINYINT DEFAULT 1,
  scheduled_publish_time TIMESTAMP NULL,
  status ENUM('draft', 'scheduled', 'published', 'failed') DEFAULT 'published',
  twitter_url TEXT,
  retweet_count INT DEFAULT 0,
  like_count INT DEFAULT 0,
  reply_count INT DEFAULT 0,
  quote_count INT DEFAULT 0,
  impression_count INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_twitter_post (twitter_post_id),
  INDEX idx_user_id (user_id),
  INDEX idx_business_id (business_id),
  INDEX idx_account_id (account_id),
  INDEX idx_status (status),
  INDEX idx_published (published),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### Method 3: Add Twitter Columns to Business Table

```sql
-- Add Twitter-related columns to business table
ALTER TABLE gmb_businesses_master 
ADD COLUMN twitter_sync_status TINYINT DEFAULT 0 COMMENT '0=Not Connected, 1=Connected, 2=Error',
ADD COLUMN twitter_account_id VARCHAR(255) NULL COMMENT 'Primary Twitter account ID for this business';

-- Add indexes for better performance
ALTER TABLE gmb_businesses_master ADD INDEX idx_twitter_sync_status (twitter_sync_status);
ALTER TABLE gmb_businesses_master ADD INDEX idx_twitter_account_id (twitter_account_id);
```

## Verification

After running any of the setup methods, verify the installation:

```sql
-- Check tables exist
SHOW TABLES LIKE 'twitter_%';

-- Check table structures
DESCRIBE twitter_oauth_tokens;
DESCRIBE twitter_accounts;
DESCRIBE twitter_posts;

-- Check business table columns
SELECT column_name, data_type, column_default 
FROM information_schema.columns 
WHERE table_schema = 'gmb' 
AND table_name = 'gmb_businesses_master' 
AND column_name IN ('twitter_sync_status', 'twitter_account_id');
```

## Troubleshooting

### Common Issues

1. **MySQL Syntax Errors**
   - Use Method 2 (Manual SQL) for older MySQL versions
   - Remove `IF NOT EXISTS` if your MySQL version doesn't support it

2. **Foreign Key Constraints**
   - Ensure parent tables exist before creating child tables
   - Check that referenced columns have the same data type

3. **Character Set Issues**
   - Ensure your MySQL supports utf8mb4
   - Use utf8 if utf8mb4 is not available

### Alternative Setup Commands

```bash
# Test database connection only
npm run test:twitter-db

# Simple table creation (compatible with older MySQL)
npm run setup:twitter-simple

# Add Twitter columns to business table
npm run add:twitter-columns
```

## Next Steps

After successful database setup:

1. Configure Twitter app credentials in `.env.development`
2. Start the application: `npm run devStart`
3. Test the Twitter integration in the Create Social Post screen

## Rollback

If you need to remove the Twitter integration:

```sql
DROP TABLE IF EXISTS twitter_posts;
DROP TABLE IF EXISTS twitter_accounts;
DROP TABLE IF EXISTS twitter_oauth_tokens;

ALTER TABLE gmb_businesses_master 
DROP COLUMN twitter_sync_status,
DROP COLUMN twitter_account_id;
```
