-- Local Falcon Integration Database Schema
-- This script creates the necessary tables for Local Falcon functionality
-- Compatible with existing geo_grid_configurations table

-- Create local_rankings table to store ranking data
CREATE TABLE IF NOT EXISTS local_rankings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  grid_configuration_id INT NOT NULL,
  grid_point_id VARCHAR(50) NOT NULL,
  business_id VARCHAR(255) NOT NULL,
  search_term VARCHAR(500) NOT NULL,
  position INT NULL,
  is_visible BOOLEAN DEFAULT FALSE,
  competitor_data JSON,
  url TEXT,
  snippet TEXT,
  check_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_grid_config (grid_configuration_id),
  INDEX idx_grid_point (grid_point_id),
  INDEX idx_business_id (business_id),
  INDEX idx_search_term (search_term(100)),
  INDEX idx_position (position),
  INDEX idx_is_visible (is_visible),
  INDEX idx_check_timestamp (check_timestamp),
  INDEX idx_created_at (created_at),
  UNIQUE KEY unique_ranking_check (grid_configuration_id, grid_point_id, business_id, search_term(100), check_timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create competitor_businesses table to manage competitor tracking
CREATE TABLE IF NOT EXISTS competitor_businesses (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  competitor_name VARCHAR(255) NOT NULL,
  business_id VARCHAR(255) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_business_id (business_id),
  INDEX idx_is_active (is_active),
  INDEX idx_competitor_name (competitor_name),
  UNIQUE KEY unique_user_competitor (user_id, competitor_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create ranking_alerts table for alert management
CREATE TABLE IF NOT EXISTS ranking_alerts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  grid_configuration_id INT NOT NULL,
  alert_type ENUM('position_drop', 'visibility_loss', 'competitor_gain') NOT NULL,
  threshold_value INT NOT NULL DEFAULT 5,
  notification_methods JSON NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  last_triggered TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_grid_config (grid_configuration_id),
  INDEX idx_alert_type (alert_type),
  INDEX idx_is_active (is_active),
  INDEX idx_last_triggered (last_triggered)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create ranking_schedules table for automated ranking checks (used by sync service)
CREATE TABLE IF NOT EXISTS ranking_schedules (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  grid_configuration_id INT NOT NULL,
  search_terms JSON NOT NULL,
  check_interval_minutes INT NOT NULL DEFAULT 60,
  is_active BOOLEAN DEFAULT TRUE,
  last_check TIMESTAMP NULL,
  next_check TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_grid_config (grid_configuration_id),
  INDEX idx_is_active (is_active),
  INDEX idx_next_check (next_check),
  INDEX idx_last_check (last_check),
  UNIQUE KEY unique_user_grid_schedule (user_id, grid_configuration_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create ranking_export_logs table to track data exports
CREATE TABLE IF NOT EXISTS ranking_export_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  grid_configuration_id INT NOT NULL,
  export_format ENUM('pdf', 'csv', 'excel') NOT NULL,
  export_options JSON,
  file_path VARCHAR(500),
  file_size_bytes INT,
  export_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
  error_message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,
  INDEX idx_user_id (user_id),
  INDEX idx_grid_config (grid_configuration_id),
  INDEX idx_export_status (export_status),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create local_falcon_config table to store Local Falcon settings per grid configuration
CREATE TABLE IF NOT EXISTS local_falcon_config (
  id INT AUTO_INCREMENT PRIMARY KEY,
  grid_configuration_id INT NOT NULL,
  search_terms JSON NOT NULL,
  tracking_enabled BOOLEAN DEFAULT FALSE,
  check_interval_minutes INT DEFAULT 60,
  businesses_to_track JSON,
  competitors_to_track JSON,
  alert_thresholds JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_grid_config (grid_configuration_id),
  INDEX idx_tracking_enabled (tracking_enabled),
  UNIQUE KEY unique_grid_config (grid_configuration_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add foreign key constraints (optional - can be added later if needed)
-- Note: These are commented out for initial setup to avoid dependency issues

-- ALTER TABLE local_rankings 
-- ADD CONSTRAINT fk_rankings_grid_config 
-- FOREIGN KEY (grid_configuration_id) REFERENCES geo_grid_configurations(id) ON DELETE CASCADE;

-- ALTER TABLE ranking_alerts 
-- ADD CONSTRAINT fk_alerts_grid_config 
-- FOREIGN KEY (grid_configuration_id) REFERENCES geo_grid_configurations(id) ON DELETE CASCADE;

-- ALTER TABLE ranking_schedules 
-- ADD CONSTRAINT fk_schedules_grid_config 
-- FOREIGN KEY (grid_configuration_id) REFERENCES geo_grid_configurations(id) ON DELETE CASCADE;

-- ALTER TABLE ranking_export_logs 
-- ADD CONSTRAINT fk_exports_grid_config 
-- FOREIGN KEY (grid_configuration_id) REFERENCES geo_grid_configurations(id) ON DELETE CASCADE;

-- ALTER TABLE local_falcon_config 
-- ADD CONSTRAINT fk_falcon_config_grid_config 
-- FOREIGN KEY (grid_configuration_id) REFERENCES geo_grid_configurations(id) ON DELETE CASCADE;

-- Create indexes for better performance on common queries
CREATE INDEX IF NOT EXISTS idx_rankings_recent ON local_rankings (grid_configuration_id, check_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_rankings_search_term_position ON local_rankings (search_term, position);
CREATE INDEX IF NOT EXISTS idx_rankings_business_visible ON local_rankings (business_id, is_visible);

-- Insert sample data for testing (optional)
-- This can be uncommented for development/testing purposes

/*
-- Sample competitor businesses
INSERT IGNORE INTO competitor_businesses (user_id, competitor_name, business_id, is_active) VALUES
(1, 'Competitor Restaurant A', 'comp_rest_a_001', TRUE),
(1, 'Competitor Restaurant B', 'comp_rest_b_002', TRUE),
(1, 'Local Cafe Chain', 'local_cafe_003', TRUE);

-- Sample ranking alerts
INSERT IGNORE INTO ranking_alerts (user_id, grid_configuration_id, alert_type, threshold_value, notification_methods, is_active) VALUES
(1, 1, 'position_drop', 5, '["email"]', TRUE),
(1, 1, 'visibility_loss', 1, '["email", "sms"]', TRUE),
(1, 1, 'competitor_gain', 3, '["email"]', TRUE);
*/
