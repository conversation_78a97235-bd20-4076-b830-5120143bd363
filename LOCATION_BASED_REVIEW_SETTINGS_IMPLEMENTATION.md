# Location-Based Review Settings Implementation

## Overview

This document outlines the implementation of location-based review reply settings for the GMB Social application. The feature allows users to configure review reply templates and auto-reply settings based on both BusinessId and LocationId, providing more granular control over review management.

## Changes Made

### 1. Database Changes

#### Tables Modified:

- **auto_reply_settings**: Added `location_id` column and updated primary key to (business_id, location_id)
- **business_reply_templates**: Added `location_id` column for location-specific template mappings

#### Database Script:

- Created `aqib-gmb-social-backend/database/setup_review_settings_location.js`
- Adds location_id columns to existing tables
- Updates primary keys and indexes for optimal performance
- Handles existing data gracefully

### 2. Backend API Changes

#### Models Updated:

- **reviewSettings.models.js**:
  - `getReplyTemplates()`: Now accepts optional locationId parameter
  - `getAutoReplySettings()`: Now supports business + location queries
  - `updateAutoReplySettings()`: Now accepts locationId parameter
  - `mapTemplateToBusinesses()`: Now supports location-specific mappings
  - `getTemplateForAutoReply()`: Now prioritizes location-specific templates

#### Controllers Updated:

- **reviewSettings.controller.js**:
  - All methods now accept locationId from query parameters
  - Updated to pass location parameters to model methods

#### Routes:

- Existing routes now support optional locationId query parameter
- No breaking changes to existing API endpoints

### 3. Frontend Changes

#### Services Updated:

- **reviewSettings.service.tsx**:
  - `IAutoReplySettings` interface: Added optional `location_id` field
  - `getReplyTemplates()`: Now accepts optional locationId parameter
  - `getAutoReplySettings()`: Now supports locationId parameter
  - `updateAutoReplySettings()`: Now accepts locationId parameter

#### Components Updated:

- **reviewSettings.screen.tsx**:

  - Added location selection dropdown alongside business selection
  - Added location loading functionality
  - Updated all service calls to include location parameters
  - Location dropdown shows "All Locations" option for business-wide settings

- **autoReplySettings.component.tsx**:
  - Updated interface to accept optional locationId
  - Updated service calls to include location parameter

### 4. Key Features

#### Location Selection:

- Business dropdown is the primary selector (mandatory)
- Location dropdown is optional and shows "All Locations" by default
- Location dropdown is populated based on selected business
- Location dropdown is disabled when no business is selected
- Changing business resets location selection to "All Locations"

#### Data Hierarchy:

- Business-only settings (location_id = NULL) apply to all locations within the business
- Location-specific settings override business-wide settings for that specific location
- Template priority: Location-specific > Business-wide > Default

#### Backward Compatibility:

- Existing business-only settings continue to work
- No breaking changes to existing API endpoints
- Graceful handling of missing location data

## Usage

### Setting Business-Wide Review Settings:

1. Select a business from the dropdown
2. Leave location dropdown as "All Locations" (default)
3. Configure templates and auto-reply settings
4. Settings apply to all locations within the business

### Setting Location-Specific Review Settings:

1. Select a business from the dropdown
2. Select a specific location from the location dropdown (populated based on business)
3. Configure templates and auto-reply settings
4. Settings apply only to the selected location and override business-wide settings

### Template Priority:

- If a location-specific template exists, it will be used
- If no location-specific template exists, business-wide template is used
- If no business template exists, default template is used

## Database Schema

### auto_reply_settings Table:

```sql
- business_id (INT, PRIMARY KEY)
- location_id (VARCHAR(255), PRIMARY KEY, NULLABLE)
- is_enabled (BOOLEAN)
- enabled_star_ratings (JSON)
- delay_minutes (INT)
- only_business_hours (BOOLEAN)
- business_hours_start (TIME)
- business_hours_end (TIME)
- updated_at (TIMESTAMP)
```

### business_reply_templates Table:

```sql
- business_id (INT)
- location_id (VARCHAR(255), NULLABLE)
- template_id (INT)
- is_active (BOOLEAN)
- created_at (TIMESTAMP)
```

## API Examples

### Get Templates for Business and Location:

```
GET /api/review-settings/templates/:userId?businessId=123&locationId=loc_456
```

### Update Auto-Reply Settings for Location:

```
PUT /api/review-settings/auto-reply/:businessId?locationId=loc_456
```

## Testing

### Database Setup:

```bash
cd aqib-gmb-social-backend
node database/setup_review_settings_location.js
```

### Verification:

1. Check that location dropdown appears in review settings
2. Verify business-wide settings work (location = "All Locations")
3. Verify location-specific settings work (select specific location)
4. Test that location-specific settings override business-wide settings
5. Verify backward compatibility with existing data

## Notes

- All changes are backward compatible
- Existing business-only settings continue to work
- Location selection is optional - defaults to business-wide
- Database migration handles existing data gracefully
- Performance optimized with proper indexes
