#!/usr/bin/env node

/**
 * Install GMB Auto Reply Service as a Windows Service
 */

require('dotenv').config();

const WindowsServiceManager = require('./services/windowsService');
const logger = require('./utils/logger');

async function installService() {
  try {
    console.log('Installing GMB Auto Reply Service...');
    console.log('=====================================');

    // Create service manager instance
    const serviceManager = new WindowsServiceManager();
    
    // Get service information
    const serviceInfo = serviceManager.getServiceInfo();
    
    console.log(`Service Name: ${serviceInfo.name}`);
    console.log(`Description: ${serviceInfo.description}`);
    console.log(`Script Path: ${serviceInfo.scriptPath}`);
    console.log('');

    // Check if running as administrator
    if (process.platform === 'win32') {
      const { execSync } = require('child_process');
      try {
        execSync('net session', { stdio: 'ignore' });
      } catch (error) {
        console.error('ERROR: This script must be run as Administrator on Windows.');
        console.error('Please run Command Prompt or PowerShell as Administrator and try again.');
        process.exit(1);
      }
    }

    // Install the service
    console.log('Installing service...');
    serviceManager.install();

    // Wait for installation to complete
    console.log('Installation initiated. Please wait...');
    
    // The service installation is asynchronous, so we'll wait a bit
    setTimeout(() => {
      console.log('');
      console.log('Installation completed!');
      console.log('');
      console.log('To start the service, run:');
      console.log('  sc start "' + serviceInfo.name + '"');
      console.log('');
      console.log('To stop the service, run:');
      console.log('  sc stop "' + serviceInfo.name + '"');
      console.log('');
      console.log('To check service status, run:');
      console.log('  sc query "' + serviceInfo.name + '"');
      console.log('');
      console.log('Service logs will be available in the ./logs directory');
      
      process.exit(0);
    }, 5000);

  } catch (error) {
    console.error('Failed to install service:', error.message);
    logger.error('Service installation failed:', error);
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log('GMB Auto Reply Service Installer');
  console.log('================================');
  console.log('');
  console.log('Usage: node install-service.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h     Show this help message');
  console.log('  --force, -f    Force installation (uninstall first if exists)');
  console.log('');
  console.log('Requirements:');
  console.log('  - Windows operating system');
  console.log('  - Administrator privileges');
  console.log('  - Node.js installed');
  console.log('');
  process.exit(0);
}

if (args.includes('--force') || args.includes('-f')) {
  console.log('Force installation requested. Uninstalling existing service first...');
  
  try {
    const serviceManager = new WindowsServiceManager();
    serviceManager.uninstall();
    
    // Wait for uninstallation to complete
    setTimeout(() => {
      installService();
    }, 3000);
    
  } catch (error) {
    console.log('No existing service found or uninstallation failed. Proceeding with installation...');
    installService();
  }
} else {
  installService();
}
