-- Quick Setup Script for Local Falcon Tables
-- Run this script to create the 3 essential tables needed for Local Falcon functionality

-- Table 1: Configurations
CREATE TABLE IF NOT EXISTS `local_falcon_configurations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `keyword` varchar(255) NOT NULL,
  `business_name` varchar(255) NOT NULL,
  `place_id` varchar(255) DEFAULT NULL,
  `center_lat` decimal(10,8) NOT NULL,
  `center_lng` decimal(11,8) NOT NULL,
  `grid_size` varchar(10) NOT NULL DEFAULT '5x5',
  `radius` decimal(8,2) NOT NULL,
  `unit` enum('meters','kilometers','miles') NOT NULL DEFAULT 'kilometers',
  `is_schedule_enabled` tinyint(1) NOT NULL DEFAULT 0,
  `schedule_frequency` enum('daily','weekly','monthly') DEFAULT NULL,
  `alert_threshold` int(11) DEFAULT NULL,
  `settings` json DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table 2: Scan Results
CREATE TABLE IF NOT EXISTS `local_falcon_scan_results` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `configuration_id` int(11) NOT NULL,
  `average_position` decimal(5,2) DEFAULT NULL,
  `visibility_percentage` decimal(5,2) DEFAULT NULL,
  `total_searches` int(11) NOT NULL DEFAULT 0,
  `found_in_results` int(11) NOT NULL DEFAULT 0,
  `rankings_data` json DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_configuration_id` (`configuration_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table 3: Alerts
CREATE TABLE IF NOT EXISTS `local_falcon_alerts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `configuration_id` int(11) NOT NULL,
  `alert_type` enum('ranking_drop','ranking_improvement','not_found','visibility_change') NOT NULL,
  `message` text NOT NULL,
  `previous_position` decimal(5,2) DEFAULT NULL,
  `current_position` decimal(5,2) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_configuration_id` (`configuration_id`),
  KEY `idx_is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
