# IIS Deployment Guide for Multiple Environments

## 🎯 **Answer to Your Question: YES, the current setup WILL work with IIS!**

I've updated your `server.js` file and created environment-specific `web.config` files to ensure proper environment loading in IIS.

## 📁 **Web.config Files Created:**

- `web.config` - Development environment (your current setup)
- `web.config.staging` - Staging environment 
- `web.config.production` - Production environment

## 🔧 **Key Changes Made:**

### 1. **Updated `server.js`**
Added environment loading logic to `server.js` (the entry point in your web.config):

```javascript
// Load environment configuration based on NODE_ENV before loading app
const environment = (process.env.NODE_ENV || "development").trim();
const envFile = `.env.${environment}`;

console.log(`Loading environment configuration from: ${envFile}`);
require("dotenv").config({ path: envFile });
```

### 2. **Environment Variables in web.config**
Each web.config now sets the appropriate `NODE_ENV`:

```xml
<environmentVariables>
  <add name="NODE_ENV" value="development" />  <!-- or staging/production -->
</environmentVariables>
```

## 🚀 **Deployment Instructions:**

### **Development Environment (Current)**
1. Use the existing `web.config` file
2. Ensure `.env.development` has correct settings
3. Deploy to IIS - it will automatically load development environment

### **Staging Environment**
1. **Replace** `web.config` with `web.config.staging`:
   ```bash
   copy web.config.staging web.config
   ```
2. **Update** `.env.staging` with your staging credentials:
   - Database host and credentials
   - AWS S3 staging bucket
   - Staging social media app credentials
   - Staging domain URLs
3. Deploy to staging IIS server

### **Production Environment**
1. **Replace** `web.config` with `web.config.production`:
   ```bash
   copy web.config.production web.config
   ```
2. **Update** `.env.production` with your production credentials:
   - Production database host and credentials
   - AWS S3 production bucket
   - Production social media app credentials
   - Production domain URLs
3. Deploy to production IIS server

## 🔍 **Verification:**

### **Check Environment Loading:**
1. After deployment, check IIS logs in `iisnode-logs` folder
2. Look for: `Loading environment configuration from: .env.{environment}`
3. Visit your API root endpoint (`/`) to see current environment

### **Expected Response:**
```json
{
  "message": "Backend is running",
  "environment": "DEVELOPMENT",  // or STAGING/PRODUCTION
  "version": "v1",
  "port": "3000"
}
```

## ⚙️ **Web.config Differences:**

| Setting | Development | Staging | Production |
|---------|-------------|---------|------------|
| **NODE_ENV** | development | staging | production |
| **devErrorsEnabled** | true | false | false |
| **httpErrors** | Detailed | Detailed | Custom |
| **scriptErrorSentToBrowser** | true | true | false |

## 🛡️ **Security Notes:**

1. **Environment Files:** Ensure `.env.*` files are deployed with correct permissions
2. **Credentials:** Use different credentials for each environment
3. **Error Handling:** Production hides detailed errors from users
4. **Logging:** All environments log to `iisnode-logs` for debugging

## 🔧 **Troubleshooting:**

### **Environment Not Loading:**
- Check `iisnode-logs` for dotenv errors
- Verify `.env.{environment}` file exists
- Ensure `NODE_ENV` is set correctly in web.config

### **Database Connection Issues:**
- Verify database credentials in environment file
- Check network connectivity from IIS server
- Review connection logs in `iisnode-logs`

### **Missing Environment Variables:**
- Run validation: `npm run validate:env:{environment}`
- Check console output for missing variables
- Update environment file with required values

## ✅ **Final Checklist:**

- [ ] `server.js` updated with environment loading
- [ ] Appropriate `web.config` file in place
- [ ] Environment file (`.env.{environment}`) configured
- [ ] Database credentials updated for environment
- [ ] AWS S3 credentials updated for environment
- [ ] Social media app credentials updated for environment
- [ ] Domain URLs updated for environment
- [ ] IIS application pool restarted after deployment

## 🎉 **Result:**

Your application will now automatically:
1. ✅ Load the correct environment based on `NODE_ENV` in web.config
2. ✅ Use environment-specific database connections
3. ✅ Use environment-specific AWS S3 buckets
4. ✅ Use environment-specific API credentials
5. ✅ Display the correct environment in API responses

**The setup is fully compatible with your existing IIS deployment!**
