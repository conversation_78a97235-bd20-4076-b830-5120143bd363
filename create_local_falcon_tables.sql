-- =====================================================
-- Local Falcon Database Tables Creation Script
-- =====================================================
-- This script creates all necessary tables for Local Falcon functionality
-- Run this script in your MySQL database

-- =====================================================
-- Table: local_falcon_configurations
-- Purpose: Store Local Falcon scan configurations
-- =====================================================
CREATE TABLE IF NOT EXISTS `local_falcon_configurations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL COMMENT 'Configuration name',
  `keyword` varchar(255) NOT NULL COMMENT 'Search keyword',
  `business_name` varchar(255) NOT NULL COMMENT 'Business name to track',
  `place_id` varchar(255) DEFAULT NULL COMMENT 'Google Place ID',
  `center_lat` decimal(10,8) NOT NULL COMMENT 'Center latitude',
  `center_lng` decimal(11,8) NOT NULL COMMENT 'Center longitude',
  `grid_size` varchar(10) NOT NULL DEFAULT '5x5' COMMENT 'Grid size (e.g., 5x5)',
  `radius` decimal(8,2) NOT NULL COMMENT 'Search radius',
  `unit` enum('meters','kilometers','miles') NOT NULL DEFAULT 'kilometers' COMMENT 'Distance unit',
  `is_schedule_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Enable scheduled scans',
  `schedule_frequency` enum('daily','weekly','monthly') DEFAULT NULL COMMENT 'Schedule frequency',
  `alert_threshold` int(11) DEFAULT NULL COMMENT 'Alert threshold for ranking changes',
  `settings` json DEFAULT NULL COMMENT 'Additional configuration settings',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_schedule_enabled` (`is_schedule_enabled`),
  CONSTRAINT `fk_local_falcon_config_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Local Falcon scan configurations';

-- =====================================================
-- Table: local_falcon_scan_results
-- Purpose: Store scan results and historical data
-- =====================================================
CREATE TABLE IF NOT EXISTS `local_falcon_scan_results` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `configuration_id` int(11) NOT NULL,
  `average_position` decimal(5,2) DEFAULT NULL COMMENT 'Average ranking position',
  `visibility_percentage` decimal(5,2) DEFAULT NULL COMMENT 'Visibility percentage',
  `total_searches` int(11) NOT NULL DEFAULT 0 COMMENT 'Total grid points searched',
  `found_in_results` int(11) NOT NULL DEFAULT 0 COMMENT 'Number of points where business was found',
  `rankings_data` json DEFAULT NULL COMMENT 'Detailed ranking data for each grid point',
  `scan_duration` int(11) DEFAULT NULL COMMENT 'Scan duration in seconds',
  `scan_status` enum('pending','running','completed','failed') NOT NULL DEFAULT 'pending',
  `error_message` text DEFAULT NULL COMMENT 'Error message if scan failed',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_configuration_id` (`configuration_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_scan_status` (`scan_status`),
  CONSTRAINT `fk_local_falcon_scan_config` FOREIGN KEY (`configuration_id`) REFERENCES `local_falcon_configurations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Local Falcon scan results and historical data';

-- =====================================================
-- Table: local_falcon_alerts
-- Purpose: Store ranking alerts and notifications
-- =====================================================
CREATE TABLE IF NOT EXISTS `local_falcon_alerts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `configuration_id` int(11) NOT NULL,
  `alert_type` enum('ranking_drop','ranking_improvement','not_found','visibility_change') NOT NULL COMMENT 'Type of alert',
  `message` text NOT NULL COMMENT 'Alert message',
  `previous_position` decimal(5,2) DEFAULT NULL COMMENT 'Previous ranking position',
  `current_position` decimal(5,2) DEFAULT NULL COMMENT 'Current ranking position',
  `previous_visibility` decimal(5,2) DEFAULT NULL COMMENT 'Previous visibility percentage',
  `current_visibility` decimal(5,2) DEFAULT NULL COMMENT 'Current visibility percentage',
  `threshold_value` decimal(5,2) DEFAULT NULL COMMENT 'Threshold that triggered the alert',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether alert has been read',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL COMMENT 'When alert was marked as read',
  PRIMARY KEY (`id`),
  KEY `idx_configuration_id` (`configuration_id`),
  KEY `idx_alert_type` (`alert_type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_local_falcon_alert_config` FOREIGN KEY (`configuration_id`) REFERENCES `local_falcon_configurations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Local Falcon ranking alerts and notifications';

-- =====================================================
-- Table: local_falcon_competitors
-- Purpose: Store competitor tracking data
-- =====================================================
CREATE TABLE IF NOT EXISTS `local_falcon_competitors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `configuration_id` int(11) NOT NULL,
  `competitor_name` varchar(255) NOT NULL COMMENT 'Competitor business name',
  `competitor_place_id` varchar(255) DEFAULT NULL COMMENT 'Competitor Google Place ID',
  `competitor_address` text DEFAULT NULL COMMENT 'Competitor address',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Whether to track this competitor',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_configuration_id` (`configuration_id`),
  KEY `idx_is_active` (`is_active`),
  UNIQUE KEY `unique_competitor_per_config` (`configuration_id`, `competitor_place_id`),
  CONSTRAINT `fk_local_falcon_competitor_config` FOREIGN KEY (`configuration_id`) REFERENCES `local_falcon_configurations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Local Falcon competitor tracking';

-- =====================================================
-- Table: local_falcon_competitor_rankings
-- Purpose: Store competitor ranking data over time
-- =====================================================
CREATE TABLE IF NOT EXISTS `local_falcon_competitor_rankings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `scan_result_id` int(11) NOT NULL,
  `competitor_id` int(11) NOT NULL,
  `average_position` decimal(5,2) DEFAULT NULL COMMENT 'Competitor average position',
  `visibility_percentage` decimal(5,2) DEFAULT NULL COMMENT 'Competitor visibility percentage',
  `total_found` int(11) NOT NULL DEFAULT 0 COMMENT 'Number of grid points where competitor was found',
  `rankings_data` json DEFAULT NULL COMMENT 'Detailed competitor ranking data',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_scan_result_id` (`scan_result_id`),
  KEY `idx_competitor_id` (`competitor_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_local_falcon_comp_ranking_scan` FOREIGN KEY (`scan_result_id`) REFERENCES `local_falcon_scan_results` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_local_falcon_comp_ranking_comp` FOREIGN KEY (`competitor_id`) REFERENCES `local_falcon_competitors` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Local Falcon competitor ranking history';

-- =====================================================
-- Table: local_falcon_scheduled_scans
-- Purpose: Store scheduled scan jobs and their status
-- =====================================================
CREATE TABLE IF NOT EXISTS `local_falcon_scheduled_scans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `configuration_id` int(11) NOT NULL,
  `scheduled_at` timestamp NOT NULL COMMENT 'When the scan is scheduled to run',
  `started_at` timestamp NULL DEFAULT NULL COMMENT 'When the scan actually started',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT 'When the scan completed',
  `status` enum('pending','running','completed','failed','cancelled') NOT NULL DEFAULT 'pending',
  `error_message` text DEFAULT NULL COMMENT 'Error message if scan failed',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_configuration_id` (`configuration_id`),
  KEY `idx_scheduled_at` (`scheduled_at`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_local_falcon_scheduled_config` FOREIGN KEY (`configuration_id`) REFERENCES `local_falcon_configurations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Local Falcon scheduled scan jobs';

-- =====================================================
-- Insert sample data (optional - for testing)
-- =====================================================
-- Uncomment the following lines if you want to insert sample data for testing

/*
-- Sample configuration (replace user_id with actual user ID)
INSERT INTO `local_falcon_configurations` (
  `user_id`, `name`, `keyword`, `business_name`, `place_id`, 
  `center_lat`, `center_lng`, `grid_size`, `radius`, `unit`,
  `is_schedule_enabled`, `schedule_frequency`, `alert_threshold`
) VALUES (
  1, 'Downtown Restaurant Scan', 'restaurant', 'Sample Restaurant', 'ChIJN1t_tDeuEmsRUsoyG83frY4',
  40.7128, -74.0060, '5x5', 1.0, 'kilometers',
  1, 'daily', 5
);

-- Sample scan result
INSERT INTO `local_falcon_scan_results` (
  `configuration_id`, `average_position`, `visibility_percentage`, 
  `total_searches`, `found_in_results`, `scan_status`
) VALUES (
  1, 8.5, 75.0, 25, 18, 'completed'
);

-- Sample alert
INSERT INTO `local_falcon_alerts` (
  `configuration_id`, `alert_type`, `message`, 
  `previous_position`, `current_position`
) VALUES (
  1, 'ranking_improvement', 'Your business ranking improved from position 12 to 8!',
  12.0, 8.0
);
*/

-- =====================================================
-- Indexes for performance optimization
-- =====================================================

-- Additional indexes for better query performance
CREATE INDEX `idx_local_falcon_config_keyword` ON `local_falcon_configurations` (`keyword`);
CREATE INDEX `idx_local_falcon_config_business` ON `local_falcon_configurations` (`business_name`);
CREATE INDEX `idx_local_falcon_scan_avg_position` ON `local_falcon_scan_results` (`average_position`);
CREATE INDEX `idx_local_falcon_scan_visibility` ON `local_falcon_scan_results` (`visibility_percentage`);
CREATE INDEX `idx_local_falcon_alert_unread` ON `local_falcon_alerts` (`is_read`, `created_at`);

-- =====================================================
-- Views for common queries (optional)
-- =====================================================

-- View for latest scan results per configuration
CREATE OR REPLACE VIEW `v_local_falcon_latest_scans` AS
SELECT 
  c.id as configuration_id,
  c.name as configuration_name,
  c.keyword,
  c.business_name,
  s.id as scan_id,
  s.average_position,
  s.visibility_percentage,
  s.total_searches,
  s.found_in_results,
  s.created_at as scan_date
FROM `local_falcon_configurations` c
LEFT JOIN `local_falcon_scan_results` s ON c.id = s.configuration_id
WHERE s.id = (
  SELECT MAX(id) 
  FROM `local_falcon_scan_results` s2 
  WHERE s2.configuration_id = c.id 
  AND s2.scan_status = 'completed'
);

-- View for unread alerts with configuration details
CREATE OR REPLACE VIEW `v_local_falcon_unread_alerts` AS
SELECT 
  a.id as alert_id,
  a.alert_type,
  a.message,
  a.previous_position,
  a.current_position,
  a.created_at as alert_date,
  c.id as configuration_id,
  c.name as configuration_name,
  c.keyword,
  c.business_name,
  c.user_id
FROM `local_falcon_alerts` a
JOIN `local_falcon_configurations` c ON a.configuration_id = c.id
WHERE a.is_read = 0
ORDER BY a.created_at DESC;

-- =====================================================
-- End of Local Falcon Tables Creation Script
-- =====================================================

-- To run this script:
-- 1. Connect to your MySQL database
-- 2. Execute this entire script
-- 3. Verify tables are created successfully
-- 4. Optionally uncomment and run the sample data inserts for testing

-- Note: Make sure the 'users' table exists before running this script
-- as it's referenced in the foreign key constraints.
