const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.development' });

async function testAIAutoReplySimple() {
  console.log('🧪 Testing AI Auto Reply Database Schema...');
  
  try {
    // Database connection using AWS RDS
    const dbConfig = {
      host: process.env.APP_DB_HOST,
      user: process.env.APP_DB_USER,
      password: process.env.APP_DB_PASSWORD,
      database: process.env.APP_DB_NAME,
    };

    console.log('Connecting to:', dbConfig.host);
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connection successful');

    // Test 1: Check if the new column exists
    console.log('\n1. Checking enable_ai_auto_reply column...');
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'auto_reply_settings' 
      AND COLUMN_NAME = 'enable_ai_auto_reply'
    `, [dbConfig.database]);

    if (columns.length > 0) {
      console.log('✅ enable_ai_auto_reply column exists:');
      console.log('   - Name:', columns[0].COLUMN_NAME);
      console.log('   - Type:', columns[0].DATA_TYPE);
      console.log('   - Default:', columns[0].COLUMN_DEFAULT);
      console.log('   - Comment:', columns[0].COLUMN_COMMENT);
    } else {
      console.log('❌ enable_ai_auto_reply column not found');
      return;
    }

    // Test 2: Show current table structure
    console.log('\n2. Current auto_reply_settings table structure:');
    const [tableStructure] = await connection.execute(`DESCRIBE auto_reply_settings`);
    tableStructure.forEach(column => {
      console.log(`   - ${column.Field}: ${column.Type} ${column.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${column.Default ? `DEFAULT ${column.Default}` : ''}`);
    });

    // Test 3: Test inserting a record with AI auto reply enabled
    console.log('\n3. Testing insert with AI auto reply enabled...');
    
    const testBusinessId = 999; // Use a test business ID
    
    // First, clean up any existing test data
    await connection.execute(`DELETE FROM auto_reply_settings WHERE business_id = ?`, [testBusinessId]);
    
    // Insert test data with AI auto reply enabled
    await connection.execute(`
      INSERT INTO auto_reply_settings 
      (business_id, is_enabled, enabled_star_ratings, delay_minutes, only_business_hours, enable_ai_auto_reply, business_hours_start, business_hours_end, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [testBusinessId, 1, JSON.stringify([4, 5]), 0, 0, 1, "09:00:00", "17:00:00"]);
    
    console.log('✅ Test record inserted successfully');

    // Test 4: Retrieve and verify the data
    console.log('\n4. Retrieving and verifying test data...');
    const [testData] = await connection.execute(`
      SELECT business_id, is_enabled, enabled_star_ratings, enable_ai_auto_reply 
      FROM auto_reply_settings 
      WHERE business_id = ?
    `, [testBusinessId]);

    if (testData.length > 0) {
      const record = testData[0];
      console.log('✅ Test data retrieved:');
      console.log('   - Business ID:', record.business_id);
      console.log('   - Enabled:', Boolean(record.is_enabled));
      console.log('   - AI Auto Reply:', Boolean(record.enable_ai_auto_reply));
      console.log('   - Star Ratings:', record.enabled_star_ratings);
    }

    // Clean up test data
    await connection.execute(`DELETE FROM auto_reply_settings WHERE business_id = ?`, [testBusinessId]);
    console.log('✅ Test data cleaned up');

    await connection.end();
    console.log('\n🎉 Database schema test completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ enable_ai_auto_reply column exists and is functional');
    console.log('   ✅ Can insert and retrieve AI auto reply settings');
    console.log('   ✅ Database schema is ready for AI auto reply feature');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

// Run the test
testAIAutoReplySimple();
