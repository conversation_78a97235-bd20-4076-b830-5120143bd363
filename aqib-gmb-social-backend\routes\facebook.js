const express = require("express");
const router = express.Router();
const { isAuthenticated } = require("../middleware/isAuthenticated");
const {
  welcome,
  authenticate,
  callback,
  callbackValidation,
  getFacebookAccounts,
  getPages,
  createPost,
  createBulkPosts,
} = require("../controllers/facebook.controller");

/**
 * @swagger
 * components:
 *   schemas:
 *     FacebookPage:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Database ID
 *         page_id:
 *           type: string
 *           description: Facebook page ID
 *         page_name:
 *           type: string
 *           description: Facebook page name
 *         page_category:
 *           type: string
 *           description: Facebook page category
 *         page_picture_url:
 *           type: string
 *           description: Facebook page picture URL
 *         is_active:
 *           type: boolean
 *           description: Whether the page is active
 *     FacebookPost:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Database post ID
 *         facebook_post_id:
 *           type: string
 *           description: Facebook post ID
 *         page_id:
 *           type: string
 *           description: Facebook page ID
 *         message:
 *           type: string
 *           description: Post message
 *         description:
 *           type: string
 *           description: Post description
 *         link:
 *           type: string
 *           description: Post link
 *         facebook_url:
 *           type: string
 *           description: Facebook post URL
 *         status:
 *           type: string
 *           description: Post status
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 */

/**
 * @swagger
 * /facebook:
 *   get:
 *     summary: Facebook API welcome endpoint
 *     tags: [Facebook]
 *     responses:
 *       200:
 *         description: Facebook API is working
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 timestamp:
 *                   type: string
 */
router.get("/", welcome);

/**
 * @swagger
 * /facebook/authenticate:
 *   post:
 *     summary: Initiate Facebook authentication
 *     tags: [Facebook]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: integer
 *                 description: User ID
 *     responses:
 *       200:
 *         description: Authentication URL generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 authUrl:
 *                   type: string
 *                 message:
 *                   type: string
 *       400:
 *         description: Missing required parameters
 *       500:
 *         description: Internal server error
 */
router.post("/authenticate", isAuthenticated, authenticate);

/**
 * @swagger
 * /facebook/callback:
 *   get:
 *     summary: Handle Facebook OAuth callback
 *     tags: [Facebook]
 *     parameters:
 *       - in: query
 *         name: code
 *         required: true
 *         schema:
 *           type: string
 *         description: Authorization code from Facebook
 *       - in: query
 *         name: state
 *         required: true
 *         schema:
 *           type: string
 *         description: State parameter containing user and business IDs
 *     responses:
 *       302:
 *         description: Redirect to frontend with authentication result
 *       400:
 *         description: Missing required parameters
 */
router.get("/callback", callback);

/**
 * @swagger
 * /facebook/callback:
 *   post:
 *     summary: Validate Facebook OAuth callback
 *     tags: [Facebook]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *               - state
 *               - userId
 *             properties:
 *               code:
 *                 type: string
 *                 description: Authorization code from Facebook
 *               state:
 *                 type: string
 *                 description: State parameter containing user ID
 *               userId:
 *                 type: integer
 *                 description: User ID for validation
 *     responses:
 *       200:
 *         description: Facebook authentication completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     pagesCount:
 *                       type: integer
 *                     pages:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           name:
 *                             type: string
 *                           category:
 *                             type: string
 *       400:
 *         description: Missing required parameters or validation error
 *       500:
 *         description: Internal server error
 */
router.post("/callback", isAuthenticated, callbackValidation);

/**
 * @swagger
 * /facebook/accounts/{userId}:
 *   get:
 *     summary: Get Facebook accounts for user
 *     tags: [Facebook]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     responses:
 *       200:
 *         description: Facebook accounts retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 accounts:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       facebook_user_id:
 *                         type: string
 *                       facebook_user_name:
 *                         type: string
 *                       facebook_user_email:
 *                         type: string
 *                       facebook_user_picture:
 *                         type: string
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                 message:
 *                   type: string
 *       400:
 *         description: Missing required parameters
 *       404:
 *         description: No Facebook accounts found
 *       401:
 *         description: Unauthorized
 */
router.get("/accounts/:userId", isAuthenticated, getFacebookAccounts);

/**
 * @swagger
 * /facebook/pages/{userId}:
 *   get:
 *     summary: Get Facebook pages for user
 *     tags: [Facebook]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     responses:
 *       200:
 *         description: Facebook pages retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 pages:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/FacebookPage'
 *                 message:
 *                   type: string
 *       400:
 *         description: Missing required parameters
 *       404:
 *         description: No Facebook pages found
 *       401:
 *         description: Unauthorized
 */
router.get("/pages/:userId", isAuthenticated, getPages);

/**
 * @swagger
 * /facebook/post/{userId}:
 *   post:
 *     summary: Create Facebook post
 *     tags: [Facebook]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - pageId
 *               - message
 *             properties:
 *               pageId:
 *                 type: string
 *                 description: Facebook page ID
 *               message:
 *                 type: string
 *                 description: Post message
 *               description:
 *                 type: string
 *                 description: Post description (optional)
 *               link:
 *                 type: string
 *                 description: Post link (optional)
 *               media:
 *                 type: array
 *                 description: Media attachments (optional)
 *                 items:
 *                   type: object
 *                   properties:
 *                     type:
 *                       type: string
 *                       enum: [image, video]
 *                     url:
 *                       type: string
 *               published:
 *                 type: boolean
 *                 description: Whether to publish immediately (default true)
 *               scheduledPublishTime:
 *                 type: string
 *                 format: date-time
 *                 description: Scheduled publish time if published is false
 *     responses:
 *       200:
 *         description: Facebook post created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/FacebookPost'
 *       400:
 *         description: Missing required parameters
 *       404:
 *         description: Facebook page not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post("/post/:userId", isAuthenticated, createPost);

module.exports = router;
