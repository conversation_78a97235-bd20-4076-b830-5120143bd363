const database = require("../config/database");
const logger = require("../utils/logger");
const moment = require("moment");

class AutoReplyModel {
  /**
   * Get all businesses/accounts/locations with auto-reply enabled
   * Now supports the new hierarchy: Business -> Account -> Location
   */
  async getEnabledAutoReplyBusinesses() {
    try {
      const query = `
        SELECT
          ars.*,
          b.id as business_id,
          b.businessName,
          b.businessEmail,
          ga.accountId as account_id,
          ga.accountName,
          gl.gmbLocationId as location_id,
          gl.gmbLocationName as location_name
        FROM auto_reply_settings ars
        JOIN gmb_businesses_master b ON ars.business_id = b.id
        LEFT JOIN gmb_accounts ga ON ars.account_id = ga.accountId
        LEFT JOIN gmb_locations gl ON ars.location_id = gl.gmbLocationId
        WHERE ars.is_enabled = 1
        ORDER BY ars.business_id, ars.account_id, ars.location_id
      `;

      const results = await database.query(query);
      return results.map((row) => {
        let enabledStarRatings = [];

        // Safely handle enabled_star_ratings (can be JSON string or already parsed array)
        if (row.enabled_star_ratings) {
          try {
            if (typeof row.enabled_star_ratings === "string") {
              // If it's a string, try to parse as JSON
              enabledStarRatings = JSON.parse(row.enabled_star_ratings);
            } else if (Array.isArray(row.enabled_star_ratings)) {
              // If it's already an array, use it directly
              enabledStarRatings = row.enabled_star_ratings;
            } else if (typeof row.enabled_star_ratings === "object") {
              // If it's an object but not an array, try to convert
              enabledStarRatings = Object.values(row.enabled_star_ratings);
            } else {
              console.warn(
                "Invalid enabled_star_ratings format:",
                typeof row.enabled_star_ratings,
                row.enabled_star_ratings
              );
              enabledStarRatings = [];
            }
          } catch (parseError) {
            console.error(
              "Error processing enabled_star_ratings for business:",
              row.business_id,
              parseError
            );
            console.error("Raw data:", row.enabled_star_ratings);
            enabledStarRatings = [];
          }
        }

        return {
          ...row,
          enabled_star_ratings: enabledStarRatings,
          // Add hierarchy level for easier processing
          hierarchy_level: row.location_id
            ? "location"
            : row.account_id
            ? "account"
            : "business",
        };
      });
    } catch (error) {
      logger.error("Error fetching enabled auto-reply businesses:", error);
      throw error;
    }
  }

  /**
   * Get pending reviews for auto-reply processing
   * Now supports filtering by business/account/location hierarchy
   */
  async getPendingReviews(autoReplyConfig) {
    try {
      const {
        business_id,
        account_id,
        location_id,
        enabled_star_ratings,
        delay_minutes,
        hierarchy_level,
      } = autoReplyConfig;

      // Convert star ratings to GMB format
      const starRatingMap = {
        1: "ONE",
        2: "TWO",
        3: "THREE",
        4: "FOUR",
        5: "FIVE",
      };

      const gmbStarRatings = enabled_star_ratings.map(
        (rating) => starRatingMap[rating]
      );

      const delayTime = moment()
        .subtract(delay_minutes, "minutes")
        .format("YYYY-MM-DD HH:mm:ss");

      const batchSize = parseInt(process.env.BATCH_SIZE) || 50;

      // Build a simpler query with direct string interpolation to avoid parameter binding issues
      let query = `
        SELECT
          gr.*,
          gl.gmbLocationName,
          gl.gmbAccountId,
          gl.gmbLocationId,
          b.id as business_id,
          CASE gr.StarRating
            WHEN 'ONE' THEN 1
            WHEN 'TWO' THEN 2
            WHEN 'THREE' THEN 3
            WHEN 'FOUR' THEN 4
            WHEN 'FIVE' THEN 5
          END AS star_rating_numeric
        FROM gmb_reviews gr
        JOIN gmb_locations gl ON gl.gmbLocationId = gr.locationId
        JOIN gmb_accounts ga ON gl.gmbAccountId = ga.accountId
        JOIN gmb_businesses_master b ON ga.businessId = b.id
        LEFT JOIN auto_reply_log arl ON gr.reviewId = arl.review_id
        WHERE b.id = ${business_id}
      `;

      // Add hierarchy-specific conditions
      if (hierarchy_level === "location" && location_id) {
        query += ` AND gl.gmbLocationId = '${location_id}'`;
      } else if (hierarchy_level === "account" && account_id) {
        query += ` AND gl.gmbAccountId = '${account_id}'`;
      }

      // Add star rating conditions
      const starRatingConditions = gmbStarRatings
        .map((rating) => `'${rating}'`)
        .join(",");
      query += ` AND gr.StarRating IN (${starRatingConditions})`;

      // Add other conditions
      query += `
          AND (gr.reviewReplyComment IS NULL OR gr.reviewReplyComment = '')
          AND gr.createTime <= '${delayTime}'
          AND arl.id IS NULL
        ORDER BY gr.createTime ASC
        LIMIT ${batchSize}
      `;

      // Use direct query without parameters to avoid binding issues
      return await database.query(query);
    } catch (error) {
      logger.error("Error fetching pending reviews:", error);
      throw error;
    }
  }

  /**
   * Get reply template for a business/account/location and star rating
   * Priority: Location-specific > Account-specific > Business-wide
   */
  async getReplyTemplate(autoReplyConfig, starRating) {
    try {
      const { business_id, account_id, location_id, hierarchy_level } =
        autoReplyConfig;

      let query = `
        SELECT rt.*
        FROM reply_templates rt
        JOIN business_reply_templates brt ON rt.id = brt.template_id
        WHERE brt.business_id = ?
          AND rt.star_rating = ?
          AND brt.is_active = 1
          AND rt.is_default = 1
      `;

      let queryParams = [business_id, starRating];

      // Add hierarchy-specific conditions with priority
      if (hierarchy_level === "location" && location_id) {
        query += ` AND (brt.location_id = ? OR brt.location_id IS NULL)`;
        queryParams.push(location_id);
        query += ` ORDER BY brt.location_id DESC`;
      } else if (hierarchy_level === "account" && account_id) {
        query += ` AND (brt.account_id = ? OR brt.account_id IS NULL) AND brt.location_id IS NULL`;
        queryParams.push(account_id);
        query += ` ORDER BY brt.account_id DESC`;
      } else {
        query += ` AND brt.account_id IS NULL AND brt.location_id IS NULL`;
      }

      query += ` LIMIT 1`;

      const results = await database.query(query, queryParams);
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      logger.error("Error fetching reply template:", error);
      throw error;
    }
  }

  /**
   * Check if current time is within business hours
   */
  isWithinBusinessHours(businessHoursStart, businessHoursEnd) {
    const now = moment();
    const currentTime = now.format("HH:mm:ss");

    // Handle cases where business hours span midnight
    if (businessHoursStart <= businessHoursEnd) {
      return (
        currentTime >= businessHoursStart && currentTime <= businessHoursEnd
      );
    } else {
      return (
        currentTime >= businessHoursStart || currentTime <= businessHoursEnd
      );
    }
  }

  /**
   * Log auto-reply attempt
   */
  async logAutoReplyAttempt(
    reviewId,
    businessId,
    accountId,
    locationId,
    templateId,
    replyContent,
    status,
    errorMessage = null
  ) {
    try {
      const query = `
        INSERT INTO auto_reply_log
        (review_id, business_id, account_id, location_id, template_id, reply_content, status, error_message, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `;

      await database.query(query, [
        reviewId,
        businessId,
        accountId,
        locationId,
        templateId,
        replyContent,
        status,
        errorMessage,
      ]);
    } catch (error) {
      logger.error("Error logging auto-reply attempt:", error);
      throw error;
    }
  }

  /**
   * Create auto_reply_log table if it doesn't exist
   */
  async createAutoReplyLogTable() {
    try {
      const query = `
        CREATE TABLE IF NOT EXISTS auto_reply_log (
          id INT AUTO_INCREMENT PRIMARY KEY,
          review_id VARCHAR(255) NOT NULL,
          business_id INT NOT NULL,
          account_id VARCHAR(255) NULL,
          location_id VARCHAR(255) NULL,
          template_id INT,
          reply_content TEXT,
          status ENUM('pending', 'success', 'failed', 'skipped') NOT NULL,
          error_message TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_review_id (review_id),
          INDEX idx_business_id (business_id),
          INDEX idx_account_id (account_id),
          INDEX idx_location_id (location_id),
          INDEX idx_status (status),
          INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `;

      await database.query(query);
      logger.info("Auto reply log table created/verified successfully");
    } catch (error) {
      logger.error("Error creating auto reply log table:", error);
      throw error;
    }
  }
}

module.exports = new AutoReplyModel();
