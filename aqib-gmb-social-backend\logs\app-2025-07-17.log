{"timestamp":"2025-07-17T06:09:20.025Z","level":"INFO","message":"Instagram Service Configuration","environment":"PRODUCTION","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T06:09:20.063Z","level":"INFO","message":"Twitter service initialized","environment":"PRODUCTION","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T06:09:20.103Z","level":"INFO","message":"Application started","environment":"PRODUCTION","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T00:39:21.000Z","level":"INFO","message":"Database connected successfully","environment":"PRODUCTION","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T07:43:26.113Z","level":"INFO","message":"Instagram Service Configuration","environment":"PRODUCTION","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T07:43:26.125Z","level":"INFO","message":"Twitter service initialized","environment":"PRODUCTION","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T07:43:26.162Z","level":"INFO","message":"Application started","environment":"PRODUCTION","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T07:43:37.359Z","level":"INFO","message":"Instagram Service Configuration","environment":"PRODUCTION","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T07:43:37.368Z","level":"INFO","message":"Twitter service initialized","environment":"PRODUCTION","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T07:43:37.404Z","level":"INFO","message":"Application started","environment":"PRODUCTION","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T02:13:38.000Z","level":"INFO","message":"Database connected successfully","environment":"PRODUCTION","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T07:55:32.943Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T07:55:32.957Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T07:55:32.998Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T07:55:33.155Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T07:55:33.167Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T07:55:33.201Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T02:25:34.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T07:55:57.977Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T07:55:57.994Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T07:55:58.033Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T07:55:58.195Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T07:55:58.206Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T07:55:58.244Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T02:25:59.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T07:56:21.216Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T07:56:21.218Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T07:56:21.235Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T07:56:21.235Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T07:56:21.280Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T07:56:21.280Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T02:26:22.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T07:58:10.722Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T07:58:10.752Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T07:58:10.849Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T02:28:11.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T07:58:11.487Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T07:58:11.525Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T07:58:11.601Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T07:58:46.054Z","level":"INFO","message":"Environment validation passed","environment":"development","totalVarsChecked":13}
{"timestamp":"2025-07-17T07:58:46.258Z","level":"INFO","message":"Environment validation passed","environment":"development","totalVarsChecked":13}
{"timestamp":"2025-07-17T07:58:51.280Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T07:58:51.296Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T07:58:51.348Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T07:58:51.535Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T02:28:52.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T07:58:51.559Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T07:58:51.629Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T07:59:03.737Z","level":"INFO","message":"Environment validation passed","environment":"development","totalVarsChecked":13}
{"timestamp":"2025-07-17T07:59:03.942Z","level":"INFO","message":"Environment validation passed","environment":"development","totalVarsChecked":13}
{"timestamp":"2025-07-17T07:59:06.874Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T07:59:06.883Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T07:59:06.921Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T07:59:07.085Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T07:59:07.095Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T07:59:07.127Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T02:29:08.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T08:00:31.947Z","level":"ERROR","message":"Missing required environment variables:","environment":"development ","missingVars":["APP_PORT","APP_ENV_NAME","APP_VER_PREFIX","APP_LOG_LEVEL","APP_DB_HOST","APP_DB_USER","APP_DB_PASSWORD","APP_DB_NAME","APP_AWS_ACCESS_KEY_ID","APP_AWS_SECRET_ACCESS_KEY","APP_AWS_REGION","APP_AWS_S3_BUCKET","APP_JWT_SECRET_KEY"],"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:93:72)\n    at validateEnvironment (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\validateEnvironment.js:62:16)\n    at [eval]:1:195\n    at Script.runInThisContext (node:vm:122:12)\n    at Object.runInThisContext (node:vm:296:38)\n    at node:internal/process/execution:83:21\n    at [eval]-wrapper:6:24\n    at runScript (node:internal/process/execution:82:62)\n    at evalScript (node:internal/process/execution:104:10)\n    at node:internal/main/eval_string:50:3"}
{"timestamp":"2025-07-17T08:00:44.550Z","level":"INFO","message":"Environment validation passed","environment":"development","totalVarsChecked":13}
{"timestamp":"2025-07-17T08:00:47.498Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T08:00:47.507Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T08:00:47.541Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T02:30:48.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T08:01:24.272Z","level":"ERROR","message":"Missing required environment variables:","environment":"development ","missingVars":["APP_PORT","APP_ENV_NAME","APP_VER_PREFIX","APP_LOG_LEVEL","APP_DB_HOST","APP_DB_USER","APP_DB_PASSWORD","APP_DB_NAME","APP_AWS_ACCESS_KEY_ID","APP_AWS_SECRET_ACCESS_KEY","APP_AWS_REGION","APP_AWS_S3_BUCKET","APP_JWT_SECRET_KEY"],"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:93:72)\n    at validateEnvironment (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\validateEnvironment.js:62:16)\n    at [eval]:1:195\n    at Script.runInThisContext (node:vm:122:12)\n    at Object.runInThisContext (node:vm:296:38)\n    at node:internal/process/execution:83:21\n    at [eval]-wrapper:6:24\n    at runScript (node:internal/process/execution:82:62)\n    at evalScript (node:internal/process/execution:104:10)\n    at node:internal/main/eval_string:50:3"}
{"timestamp":"2025-07-17T08:01:53.637Z","level":"INFO","message":"Environment validation passed","environment":"development","totalVarsChecked":13}
{"timestamp":"2025-07-17T08:01:56.304Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T08:01:56.315Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T08:01:56.357Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T02:31:57.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T08:02:46.103Z","level":"INFO","message":"Environment validation passed","environment":"development","totalVarsChecked":13}
{"timestamp":"2025-07-17T08:02:49.788Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T08:02:49.803Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T08:02:49.852Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T02:32:50.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T08:03:35.836Z","level":"INFO","message":"Environment validation passed","environment":"development","totalVarsChecked":13}
{"timestamp":"2025-07-17T08:03:52.973Z","level":"INFO","message":"Environment validation passed","environment":"staging","totalVarsChecked":13}
{"timestamp":"2025-07-17T08:04:47.503Z","level":"ERROR","message":"Missing required environment variables:","environment":"development ","missingVars":["APP_PORT","APP_ENV_NAME","APP_VER_PREFIX","APP_LOG_LEVEL","APP_DB_HOST","APP_DB_USER","APP_DB_PASSWORD","APP_DB_NAME","APP_AWS_ACCESS_KEY_ID","APP_AWS_SECRET_ACCESS_KEY","APP_AWS_REGION","APP_AWS_S3_BUCKET","APP_JWT_SECRET_KEY"],"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:93:72)\n    at validateEnvironment (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\validateEnvironment.js:62:16)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\app.js:17:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\app.local.js:2:13)"}
{"timestamp":"2025-07-17T08:05:51.085Z","level":"INFO","message":"Environment validation passed","environment":"development","totalVarsChecked":13}
{"timestamp":"2025-07-17T08:05:51.130Z","level":"ERROR","message":"Missing required environment variables:","environment":"development ","missingVars":["APP_PORT","APP_ENV_NAME","APP_VER_PREFIX","APP_LOG_LEVEL","APP_DB_HOST","APP_DB_USER","APP_DB_PASSWORD","APP_DB_NAME","APP_AWS_ACCESS_KEY_ID","APP_AWS_SECRET_ACCESS_KEY","APP_AWS_REGION","APP_AWS_S3_BUCKET","APP_JWT_SECRET_KEY"],"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:93:72)\n    at validateEnvironment (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\validateEnvironment.js:62:16)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\app.js:17:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\app.local.js:10:13)"}
{"timestamp":"2025-07-17T08:05:53.984Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T08:05:53.995Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T08:05:54.033Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T02:35:55.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T08:06:17.629Z","level":"ERROR","message":"Missing required environment variables:","environment":"development ","missingVars":["APP_PORT","APP_ENV_NAME","APP_VER_PREFIX","APP_LOG_LEVEL","APP_DB_HOST","APP_DB_USER","APP_DB_PASSWORD","APP_DB_NAME","APP_AWS_ACCESS_KEY_ID","APP_AWS_SECRET_ACCESS_KEY","APP_AWS_REGION","APP_AWS_S3_BUCKET","APP_JWT_SECRET_KEY"],"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:93:72)\n    at validateEnvironment (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\validateEnvironment.js:62:16)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\app.js:11:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\app.local.js:10:13)"}
{"timestamp":"2025-07-17T08:06:17.780Z","level":"INFO","message":"Environment validation passed","environment":"development","totalVarsChecked":13}
{"timestamp":"2025-07-17T08:06:20.699Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T08:06:20.712Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T08:06:20.751Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T02:36:21.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T08:07:23.950Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T08:07:23.964Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T08:07:24.004Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T08:07:24.016Z","level":"INFO","message":"Environment validation passed","environment":"development","totalVarsChecked":13}
{"timestamp":"2025-07-17T02:37:25.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T08:07:51.771Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T08:07:51.793Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T08:07:51.858Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T02:37:52.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T08:08:11.604Z","level":"INFO","message":"Instagram Service Configuration","environment":"development","hasClientId":false,"hasClientSecret":false,"hasRedirectUri":false,"clientId":"undefined","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T08:08:11.619Z","level":"INFO","message":"Twitter service initialized","environment":"development","hasClientId":false,"hasClientSecret":false,"tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T08:08:11.659Z","level":"INFO","message":"Application started","port":3000}
{"timestamp":"2025-07-17T08:11:30.672Z","level":"INFO","message":"Instagram Service Configuration","environment":"development","hasClientId":false,"hasClientSecret":false,"hasRedirectUri":false,"clientId":"undefined","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T08:11:30.682Z","level":"INFO","message":"Twitter service initialized","environment":"development","hasClientId":false,"hasClientSecret":false,"tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T08:11:30.720Z","level":"INFO","message":"Application started","port":3000}
{"timestamp":"2025-07-17T08:11:30.736Z","level":"ERROR","message":"Database connection failed","environment":"development","error":"","stack":"AggregateError\n    at internalConnectMultiple (node:net:1114:18)\n    at afterConnectMultiple (node:net:1667:5)"}
{"timestamp":"2025-07-17T08:12:57.005Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T08:12:57.017Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T08:12:57.067Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T02:43:00.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T08:13:59.947Z","level":"INFO","message":"Instagram Service Configuration","environment":"STAGING","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"your-sta...","redirectUri":"https://your-staging-domain.com/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T08:13:59.961Z","level":"INFO","message":"Twitter service initialized","environment":"STAGING","hasClientId":true,"hasClientSecret":true,"redirectUri":"https://your-staging-frontend-domain.com/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T08:13:59.991Z","level":"INFO","message":"Application started","environment":"STAGING","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T08:14:00.021Z","level":"ERROR","message":"Database connection failed","environment":"STAGING","error":"getaddrinfo ENOTFOUND your-staging-db-host.amazonaws.com","stack":"Error: getaddrinfo ENOTFOUND your-staging-db-host.amazonaws.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:118:26)","database":"gmb_staging","host":"your-staging-db-host.amazonaws.com"}
{"timestamp":"2025-07-17T08:18:01.130Z","level":"INFO","message":"Environment validation passed","environment":"production","totalVarsChecked":13}
{"timestamp":"2025-07-17T08:18:51.050Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T08:18:51.064Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T08:18:51.101Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T02:48:52.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T08:22:54.689Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T08:22:54.700Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T08:22:54.737Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T02:52:55.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T08:31:20.234Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T08:31:20.249Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T08:31:20.298Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T03:01:21.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T09:40:19.964Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T09:40:19.991Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T09:40:20.023Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T04:10:21.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T09:40:40.089Z","level":"INFO","message":"Instagram Service Configuration","environment":"STAGING","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"your-sta...","redirectUri":"https://your-staging-domain.com/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T09:40:40.101Z","level":"INFO","message":"Twitter service initialized","environment":"STAGING","hasClientId":true,"hasClientSecret":true,"redirectUri":"https://your-staging-frontend-domain.com/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T09:40:40.141Z","level":"INFO","message":"Application started","environment":"STAGING","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T04:10:41.000Z","level":"INFO","message":"Database connected successfully","environment":"STAGING","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T09:47:50.726Z","level":"INFO","message":"Instagram Service Configuration","environment":"STAGING","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"your-sta...","redirectUri":"https://your-staging-domain.com/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T09:47:50.740Z","level":"INFO","message":"Twitter service initialized","environment":"STAGING","hasClientId":true,"hasClientSecret":true,"redirectUri":"https://your-staging-frontend-domain.com/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T09:47:50.778Z","level":"INFO","message":"Application started","environment":"STAGING","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T09:54:43.337Z","level":"INFO","message":"Instagram Service Configuration","environment":"STAGING","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"https://devapi.mylocobiz.com/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T09:54:43.349Z","level":"INFO","message":"Twitter service initialized","environment":"STAGING","hasClientId":true,"hasClientSecret":true,"redirectUri":"https://dev.mylocobiz.com/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T09:54:43.400Z","level":"INFO","message":"Application started","environment":"STAGING","version":"v1","port":"3001"}
{"timestamp":"2025-07-17T04:24:44.000Z","level":"INFO","message":"Database connected successfully","environment":"STAGING","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T10:03:44.897Z","level":"INFO","message":"Instagram Service Configuration","environment":"STAGING","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"https://devapi.mylocobiz.com/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T10:03:44.915Z","level":"INFO","message":"Twitter service initialized","environment":"STAGING","hasClientId":true,"hasClientSecret":true,"redirectUri":"https://dev.mylocobiz.com/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T10:03:44.960Z","level":"INFO","message":"Application started","environment":"STAGING","version":"v1","port":"3001"}
{"timestamp":"2025-07-17T04:33:46.000Z","level":"INFO","message":"Database connected successfully","environment":"STAGING","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T13:46:09.422Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T13:46:09.437Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T13:46:09.487Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T08:16:10.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T14:02:12.886Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"8b6a942f-d677-43dd-a18a-f7c00b50d2b4","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-07-17T14:02:12.888Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"8b6a942f-d677-43dd-a18a-f7c00b50d2b4","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-07-17T14:02:13.029Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"8b6a942f-d677-43dd-a18a-f7c00b50d2b4","userId":52,"email":"<EMAIL>"}
{"timestamp":"2025-07-17T14:02:27.138Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"8b3efc17-8407-4d79-a4a5-815b4d21f6e4","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T14:02:27.141Z","level":"ERROR","message":"Error getting Local Falcon alerts:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:305:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:771:20)","userId":"52","unreadOnly":false}
{"timestamp":"2025-07-17T14:02:27.143Z","level":"ERROR","message":"Error getting alerts:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:305:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:771:20)","requestId":"8b3efc17-8407-4d79-a4a5-815b4d21f6e4"}
{"timestamp":"2025-07-17T14:02:27.150Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"c7852be3-8276-45d6-860b-1609db237023","userId":"52"}
{"timestamp":"2025-07-17T14:02:27.155Z","level":"ERROR","message":"Error getting Local Falcon configurations:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:88:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:637:28)","userId":"52"}
{"timestamp":"2025-07-17T14:02:27.157Z","level":"ERROR","message":"Error getting configurations:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:88:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:637:28)","requestId":"c7852be3-8276-45d6-860b-1609db237023"}
{"timestamp":"2025-07-17T14:02:32.780Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"1b13a46a-d0ae-40f9-a6a7-baf844b96a0b","query":"hyf"}
{"timestamp":"2025-07-17T14:02:34.683Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"1b13a46a-d0ae-40f9-a6a7-baf844b96a0b","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:02:38.460Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"8c7b4b4d-64cd-4308-b2c9-a67eee772744","query":"hyd"}
{"timestamp":"2025-07-17T14:02:38.602Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"eb4bd0a8-3ae0-4f2d-96b6-619497f6e12f","query":"hyde"}
{"timestamp":"2025-07-17T14:02:38.765Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"a0b151f9-0e93-4827-8237-72800f486ec1","query":"hyder"}
{"timestamp":"2025-07-17T14:02:38.864Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"8c7b4b4d-64cd-4308-b2c9-a67eee772744","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:02:39.785Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"c21eb1f4-7e49-414d-a499-778f47529bb5","query":"hydera"}
{"timestamp":"2025-07-17T14:02:39.998Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"eb4bd0a8-3ae0-4f2d-96b6-619497f6e12f","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:02:40.176Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"c21eb1f4-7e49-414d-a499-778f47529bb5","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:02:40.274Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"a0b151f9-0e93-4827-8237-72800f486ec1","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:20:22.790Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"57911e80-5201-4089-9fc8-71d19afac574","controller":"business","action":"businessList","userId":"132"}
{"timestamp":"2025-07-17T14:20:22.794Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"57911e80-5201-4089-9fc8-71d19afac574","userId":"132"}
{"timestamp":"2025-07-17T14:20:22.812Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6bf50bbc-0f0f-4419-a16d-46ecb8299a42","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-07-17T14:20:22.857Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"57911e80-5201-4089-9fc8-71d19afac574","userId":"132","businessCount":1}
{"timestamp":"2025-07-17T14:21:18.280Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"5436a4db-2c6c-414d-9c44-eb21fcf51ea1","controller":"business","action":"businessList","userId":"132"}
{"timestamp":"2025-07-17T14:21:18.282Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"5436a4db-2c6c-414d-9c44-eb21fcf51ea1","userId":"132"}
{"timestamp":"2025-07-17T14:21:18.293Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"deb2c74c-8c78-4f39-9625-ea0d81f7c25b","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-07-17T14:21:18.316Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"3d8a3626-9cbc-4b9e-9ef8-94746e864070","userId":"52"}
{"timestamp":"2025-07-17T14:21:18.325Z","level":"ERROR","message":"Error getting Local Falcon configurations:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:88:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:637:28)","userId":"52"}
{"timestamp":"2025-07-17T14:21:18.327Z","level":"ERROR","message":"Error getting configurations:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:88:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:637:28)","requestId":"3d8a3626-9cbc-4b9e-9ef8-94746e864070"}
{"timestamp":"2025-07-17T14:21:18.339Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"c5aa51a0-d597-4f7d-9900-fa525ed4e3a1","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T14:21:18.391Z","level":"ERROR","message":"Error getting Local Falcon alerts:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:305:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:771:20)","userId":"52","unreadOnly":false}
{"timestamp":"2025-07-17T14:21:18.393Z","level":"ERROR","message":"Error getting alerts:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:305:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:771:20)","requestId":"c5aa51a0-d597-4f7d-9900-fa525ed4e3a1"}
{"timestamp":"2025-07-17T14:21:18.434Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"5436a4db-2c6c-414d-9c44-eb21fcf51ea1","userId":"132","businessCount":1}
{"timestamp":"2025-07-17T14:32:00.006Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"224551fc-2471-44fc-bf4e-3df11e1019d2","userId":"52"}
{"timestamp":"2025-07-17T14:32:00.019Z","level":"ERROR","message":"Error getting Local Falcon configurations:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:88:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:637:28)","userId":"52"}
{"timestamp":"2025-07-17T14:32:00.022Z","level":"ERROR","message":"Error getting configurations:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:88:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:637:28)","requestId":"224551fc-2471-44fc-bf4e-3df11e1019d2"}
{"timestamp":"2025-07-17T14:32:00.100Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"12e2ad39-544b-4b9f-9ad0-79be2aeef628","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T14:32:00.142Z","level":"ERROR","message":"Error getting Local Falcon alerts:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:305:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:771:20)","userId":"52","unreadOnly":false}
{"timestamp":"2025-07-17T14:32:00.150Z","level":"ERROR","message":"Error getting alerts:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:305:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:771:20)","requestId":"12e2ad39-544b-4b9f-9ad0-79be2aeef628"}
{"timestamp":"2025-07-17T14:32:35.975Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"26268d37-26e2-47fb-b25a-60f69ed876c9","query":"sri"}
{"timestamp":"2025-07-17T14:32:37.472Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"26268d37-26e2-47fb-b25a-60f69ed876c9","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:32:41.483Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"3447c4b9-12f7-4643-932c-55bbfbb87434","query":"srie"}
{"timestamp":"2025-07-17T14:32:41.937Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"3447c4b9-12f7-4643-932c-55bbfbb87434","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:32:42.179Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"06265a15-e63f-45fc-a0bc-c2c08b817580","query":"sriey"}
{"timestamp":"2025-07-17T14:32:42.373Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"a150a37e-f86f-4b6d-a172-ca4f6087c5b5","query":"srieye"}
{"timestamp":"2025-07-17T14:32:42.710Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"06265a15-e63f-45fc-a0bc-c2c08b817580","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:32:43.541Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"a150a37e-f86f-4b6d-a172-ca4f6087c5b5","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:32:43.748Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"eee03a4b-9f3d-4aa4-8900-f3dd0935d8cc","query":"srieyec"}
{"timestamp":"2025-07-17T14:32:43.916Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"b4466a1f-49c1-4f00-ac5c-2324740cbfe0","query":"srieyeca"}
{"timestamp":"2025-07-17T14:32:44.142Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"30346fc7-8ef5-450e-89da-3b28e7a32ef2","query":"srieyecar"}
{"timestamp":"2025-07-17T14:32:44.189Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"eee03a4b-9f3d-4aa4-8900-f3dd0935d8cc","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:32:44.305Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"73cca516-d540-4ecd-bf02-97372ebeade0","query":"srieyecare"}
{"timestamp":"2025-07-17T14:32:44.373Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"b4466a1f-49c1-4f00-ac5c-2324740cbfe0","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:32:44.759Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"73cca516-d540-4ecd-bf02-97372ebeade0","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:32:45.089Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"30346fc7-8ef5-450e-89da-3b28e7a32ef2","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:33:03.606Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"264dd29e-d51d-4521-8e15-ec55220cff63","query":"sri"}
{"timestamp":"2025-07-17T14:33:03.783Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"8016206c-a867-4ef6-bf36-a8d60773d134","query":"sri "}
{"timestamp":"2025-07-17T14:33:04.083Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"99e29448-e721-48bd-8ea3-aea0399da1eb","query":"sri e"}
{"timestamp":"2025-07-17T14:33:04.279Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"d7f7ba40-b2be-455c-bda3-418a078a8ba1","query":"sri ey"}
{"timestamp":"2025-07-17T14:33:04.498Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"f8b7d4d7-d131-437c-9226-4c994f6ccf62","query":"sri eye"}
{"timestamp":"2025-07-17T14:33:04.699Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"264dd29e-d51d-4521-8e15-ec55220cff63","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:33:04.704Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"8016206c-a867-4ef6-bf36-a8d60773d134","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:33:04.992Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"99e29448-e721-48bd-8ea3-aea0399da1eb","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:33:05.197Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"d7f7ba40-b2be-455c-bda3-418a078a8ba1","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:33:05.398Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"f8b7d4d7-d131-437c-9226-4c994f6ccf62","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:33:08.656Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"93db828d-557c-4ee5-a5ae-f84b3c2c871a","query":"sri eye "}
{"timestamp":"2025-07-17T14:33:09.141Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"93db828d-557c-4ee5-a5ae-f84b3c2c871a","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:33:09.487Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"9263a771-42bb-4c37-bc18-de5d933c803d","query":"sri eye c"}
{"timestamp":"2025-07-17T14:33:09.882Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"9263a771-42bb-4c37-bc18-de5d933c803d","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:33:10.068Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"d61c2700-00ef-4482-bfab-55d228512af9","query":"sri eye ca"}
{"timestamp":"2025-07-17T14:33:10.461Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"d61c2700-00ef-4482-bfab-55d228512af9","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:33:11.785Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"630c48d9-94d8-4c30-85e2-55a641559d44","query":"sri eye car"}
{"timestamp":"2025-07-17T14:33:12.182Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"630c48d9-94d8-4c30-85e2-55a641559d44","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:33:12.370Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"c6667030-be62-42c8-888e-ae5517fc285e","query":"sri eye care"}
{"timestamp":"2025-07-17T14:33:12.756Z","level":"WARN","message":"Local Falcon API call failed, using fallback","environment":"DEVELOPMENT","requestId":"c6667030-be62-42c8-888e-ae5517fc285e","error":"Request failed with status code 401"}
{"timestamp":"2025-07-17T14:38:52.594Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T14:38:52.609Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T14:38:52.655Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T09:08:53.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T15:07:25.190Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"5d246378-8880-4471-af74-cc5fae586e9b","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T15:07:25.201Z","level":"ERROR","message":"Error getting Local Falcon alerts:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:305:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:659:20)","userId":"52","unreadOnly":false}
{"timestamp":"2025-07-17T15:07:25.203Z","level":"ERROR","message":"Error getting alerts:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:305:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:659:20)","requestId":"5d246378-8880-4471-af74-cc5fae586e9b"}
{"timestamp":"2025-07-17T15:07:25.218Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"a23d4a78-457c-4508-8545-d780582ca10a","userId":"52"}
{"timestamp":"2025-07-17T15:07:25.227Z","level":"ERROR","message":"Error getting Local Falcon configurations:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:88:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","userId":"52"}
{"timestamp":"2025-07-17T15:07:25.230Z","level":"ERROR","message":"Error getting configurations:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:88:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","requestId":"a23d4a78-457c-4508-8545-d780582ca10a"}
{"timestamp":"2025-07-17T15:11:08.201Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"1f9ec259-875c-4a4c-995d-8bc8c1e1dc2c","userId":"52"}
{"timestamp":"2025-07-17T15:11:08.205Z","level":"ERROR","message":"Error getting Local Falcon configurations:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:88:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","userId":"52"}
{"timestamp":"2025-07-17T15:11:08.207Z","level":"ERROR","message":"Error getting configurations:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:88:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","requestId":"1f9ec259-875c-4a4c-995d-8bc8c1e1dc2c"}
{"timestamp":"2025-07-17T15:11:08.217Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"6f3cce02-f1fd-4d5a-aaf1-481c430307b9","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T15:11:08.228Z","level":"ERROR","message":"Error getting Local Falcon alerts:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:305:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:659:20)","userId":"52","unreadOnly":false}
{"timestamp":"2025-07-17T15:11:08.232Z","level":"ERROR","message":"Error getting alerts:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:305:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:659:20)","requestId":"6f3cce02-f1fd-4d5a-aaf1-481c430307b9"}
{"timestamp":"2025-07-17T15:11:23.684Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"86d21b20-b1b7-4ce5-9411-a35436357483","controller":"googlePlaces","action":"getServiceAreaSuggestions","query":"sea"}
{"timestamp":"2025-07-17T15:11:23.685Z","level":"INFO","message":"Fetching service area suggestions from Google Places API","environment":"DEVELOPMENT","requestId":"86d21b20-b1b7-4ce5-9411-a35436357483","query":"sea"}
{"timestamp":"2025-07-17T15:11:24.086Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"965cfde7-1eb9-45f0-8ada-b65fa5897a1f","controller":"googlePlaces","action":"getServiceAreaSuggestions","query":"sear"}
{"timestamp":"2025-07-17T15:11:24.088Z","level":"INFO","message":"Fetching service area suggestions from Google Places API","environment":"DEVELOPMENT","requestId":"965cfde7-1eb9-45f0-8ada-b65fa5897a1f","query":"sear"}
{"timestamp":"2025-07-17T15:11:24.260Z","level":"INFO","message":"Service area suggestions fetched successfully","environment":"DEVELOPMENT","requestId":"86d21b20-b1b7-4ce5-9411-a35436357483","query":"sea","suggestionCount":3}
{"timestamp":"2025-07-17T15:11:24.409Z","level":"INFO","message":"Service area suggestions fetched successfully","environment":"DEVELOPMENT","requestId":"965cfde7-1eb9-45f0-8ada-b65fa5897a1f","query":"sear","suggestionCount":5}
{"timestamp":"2025-07-17T15:11:30.131Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"df6b27f2-e2f7-4137-a09f-4c894dc66861","controller":"googlePlaces","action":"getServiceAreaSuggestions","query":"sea"}
{"timestamp":"2025-07-17T15:11:30.132Z","level":"INFO","message":"Fetching service area suggestions from Google Places API","environment":"DEVELOPMENT","requestId":"df6b27f2-e2f7-4137-a09f-4c894dc66861","query":"sea"}
{"timestamp":"2025-07-17T15:11:30.318Z","level":"INFO","message":"Service area suggestions fetched successfully","environment":"DEVELOPMENT","requestId":"df6b27f2-e2f7-4137-a09f-4c894dc66861","query":"sea","suggestionCount":3}
{"timestamp":"2025-07-17T15:11:31.679Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"52d70b29-a580-456b-b017-112c4cf0a4f7","controller":"googlePlaces","action":"getServiceAreaSuggestions","query":"Seawoods, Navi Mumbai, Maharashtra, India"}
{"timestamp":"2025-07-17T15:11:31.680Z","level":"INFO","message":"Fetching service area suggestions from Google Places API","environment":"DEVELOPMENT","requestId":"52d70b29-a580-456b-b017-112c4cf0a4f7","query":"Seawoods, Navi Mumbai, Maharashtra, India"}
{"timestamp":"2025-07-17T15:11:31.687Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e1741db9-931a-442f-a3b7-84e09fadac92","controller":"googlePlaces","action":"getPlaceDetails","placeId":"ChIJA90z7JjD5zsRI9pvYwU2jIA"}
{"timestamp":"2025-07-17T15:11:31.690Z","level":"INFO","message":"Fetching place details from Google Places API","environment":"DEVELOPMENT","requestId":"e1741db9-931a-442f-a3b7-84e09fadac92","placeId":"ChIJA90z7JjD5zsRI9pvYwU2jIA"}
{"timestamp":"2025-07-17T15:11:31.807Z","level":"INFO","message":"Service area suggestions fetched successfully","environment":"DEVELOPMENT","requestId":"52d70b29-a580-456b-b017-112c4cf0a4f7","query":"Seawoods, Navi Mumbai, Maharashtra, India","suggestionCount":4}
{"timestamp":"2025-07-17T15:11:31.867Z","level":"INFO","message":"Place details fetched successfully","environment":"DEVELOPMENT","requestId":"e1741db9-931a-442f-a3b7-84e09fadac92","placeId":"ChIJA90z7JjD5zsRI9pvYwU2jIA","placeName":"Seawoods"}
{"timestamp":"2025-07-17T15:14:56.193Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"5fe1df98-1639-4c47-9018-27e8b8ba552d","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T15:14:56.196Z","level":"ERROR","message":"Error getting Local Falcon alerts:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:305:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:659:20)","userId":"52","unreadOnly":false}
{"timestamp":"2025-07-17T15:14:56.197Z","level":"ERROR","message":"Error getting alerts:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:305:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:659:20)","requestId":"5fe1df98-1639-4c47-9018-27e8b8ba552d"}
{"timestamp":"2025-07-17T15:14:56.203Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"76cd6e9c-13b9-4147-af75-1879686894bf","userId":"52"}
{"timestamp":"2025-07-17T15:14:56.207Z","level":"ERROR","message":"Error getting Local Falcon configurations:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:88:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","userId":"52"}
{"timestamp":"2025-07-17T15:14:56.208Z","level":"ERROR","message":"Error getting configurations:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:88:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","requestId":"76cd6e9c-13b9-4147-af75-1879686894bf"}
{"timestamp":"2025-07-17T15:15:56.851Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"940d6ef1-065c-4c34-8db5-6e8cd89dfc59","controller":"googlePlaces","action":"getServiceAreaSuggestions","query":"Ban"}
{"timestamp":"2025-07-17T15:15:56.853Z","level":"INFO","message":"Fetching service area suggestions from Google Places API","environment":"DEVELOPMENT","requestId":"940d6ef1-065c-4c34-8db5-6e8cd89dfc59","query":"Ban"}
{"timestamp":"2025-07-17T15:15:57.052Z","level":"INFO","message":"Service area suggestions fetched successfully","environment":"DEVELOPMENT","requestId":"940d6ef1-065c-4c34-8db5-6e8cd89dfc59","query":"Ban","suggestionCount":5}
{"timestamp":"2025-07-17T15:15:57.329Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"2b65f49d-6212-45e3-9e7c-69f954b7a473","controller":"googlePlaces","action":"getServiceAreaSuggestions","query":"Bang"}
{"timestamp":"2025-07-17T15:15:57.331Z","level":"INFO","message":"Fetching service area suggestions from Google Places API","environment":"DEVELOPMENT","requestId":"2b65f49d-6212-45e3-9e7c-69f954b7a473","query":"Bang"}
{"timestamp":"2025-07-17T15:15:57.446Z","level":"INFO","message":"Service area suggestions fetched successfully","environment":"DEVELOPMENT","requestId":"2b65f49d-6212-45e3-9e7c-69f954b7a473","query":"Bang","suggestionCount":5}
{"timestamp":"2025-07-17T15:15:58.793Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e397880b-7bc0-4b6a-b0d6-5b70e37e8580","controller":"googlePlaces","action":"getServiceAreaSuggestions","query":"Bangalore, Karnataka, India"}
{"timestamp":"2025-07-17T15:15:58.794Z","level":"INFO","message":"Fetching service area suggestions from Google Places API","environment":"DEVELOPMENT","requestId":"e397880b-7bc0-4b6a-b0d6-5b70e37e8580","query":"Bangalore, Karnataka, India"}
{"timestamp":"2025-07-17T15:15:58.799Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b664fda2-ba43-4f93-bf4e-dfe2dfa6c86b","controller":"googlePlaces","action":"getPlaceDetails","placeId":"ChIJbU60yXAWrjsR4E9-UejD3_g"}
{"timestamp":"2025-07-17T15:15:58.801Z","level":"INFO","message":"Fetching place details from Google Places API","environment":"DEVELOPMENT","requestId":"b664fda2-ba43-4f93-bf4e-dfe2dfa6c86b","placeId":"ChIJbU60yXAWrjsR4E9-UejD3_g"}
{"timestamp":"2025-07-17T15:15:58.915Z","level":"INFO","message":"Service area suggestions fetched successfully","environment":"DEVELOPMENT","requestId":"e397880b-7bc0-4b6a-b0d6-5b70e37e8580","query":"Bangalore, Karnataka, India","suggestionCount":4}
{"timestamp":"2025-07-17T15:15:59.022Z","level":"INFO","message":"Place details fetched successfully","environment":"DEVELOPMENT","requestId":"b664fda2-ba43-4f93-bf4e-dfe2dfa6c86b","placeId":"ChIJbU60yXAWrjsR4E9-UejD3_g","placeName":"Bengaluru"}
{"timestamp":"2025-07-17T15:16:04.306Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"7f9b19e0-c866-4e9b-ab9d-acc9535bf677","query":"sri"}
{"timestamp":"2025-07-17T15:16:08.314Z","level":"INFO","message":"Local Falcon API response received","environment":"DEVELOPMENT","requestId":"7f9b19e0-c866-4e9b-ab9d-acc9535bf677","count":20}
{"timestamp":"2025-07-17T15:16:10.370Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"33aabb8e-4192-42a2-8a0b-f3efc577149e","query":"sri "}
{"timestamp":"2025-07-17T15:16:10.719Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"8e90528b-6b46-4d5a-a7d9-9672de6b4cd0","query":"sri e"}
{"timestamp":"2025-07-17T15:16:12.370Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"72fc3ca7-38ce-44f6-a238-1db8f7b0cb67","query":"sri ey"}
{"timestamp":"2025-07-17T15:16:13.133Z","level":"INFO","message":"Local Falcon API response received","environment":"DEVELOPMENT","requestId":"33aabb8e-4192-42a2-8a0b-f3efc577149e","count":5}
{"timestamp":"2025-07-17T15:16:13.839Z","level":"INFO","message":"Local Falcon API response received","environment":"DEVELOPMENT","requestId":"8e90528b-6b46-4d5a-a7d9-9672de6b4cd0","count":0}
{"timestamp":"2025-07-17T15:16:13.894Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"a4d879df-bb99-4d8b-a405-38733ab3d7bb","query":"sri eye"}
{"timestamp":"2025-07-17T15:16:16.485Z","level":"INFO","message":"Local Falcon API response received","environment":"DEVELOPMENT","requestId":"72fc3ca7-38ce-44f6-a238-1db8f7b0cb67","count":1}
{"timestamp":"2025-07-17T15:16:17.874Z","level":"INFO","message":"Local Falcon API response received","environment":"DEVELOPMENT","requestId":"a4d879df-bb99-4d8b-a405-38733ab3d7bb","count":1}
{"timestamp":"2025-07-17T15:16:19.424Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"9b4d6430-6299-4786-8c64-e0cd43185755","query":"Sri Eye Care Speciality Eye Hospital"}
{"timestamp":"2025-07-17T15:16:19.648Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"0f60838f-e41f-4e48-880b-47a37f5c3367","query":"Sri Eye Care Speciality Eye Hospital","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:16:22.190Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"0f60838f-e41f-4e48-880b-47a37f5c3367","count":1}
{"timestamp":"2025-07-17T15:16:22.237Z","level":"INFO","message":"Calculating Local Falcon grid","environment":"DEVELOPMENT","requestId":"570aa430-a3c9-404b-92bf-761d3fb465c5","lat":13.0196844,"lng":77.6285592,"gridSize":"5x5","radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:16:22.336Z","level":"INFO","message":"Local Falcon API response received","environment":"DEVELOPMENT","requestId":"9b4d6430-6299-4786-8c64-e0cd43185755","count":1}
{"timestamp":"2025-07-17T15:16:50.170Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"5185dd4b-f515-49a9-a3f1-28459a5ff3a8","query":"sri","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:16:53.827Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"0577002c-bbde-4e0f-a6a7-b796e9a1407a","query":"sri ","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:16:54.247Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"5185dd4b-f515-49a9-a3f1-28459a5ff3a8","count":20}
{"timestamp":"2025-07-17T15:16:56.889Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"ca400444-7692-437d-a00e-d3eb57393aeb","query":"sri e","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:16:57.151Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"128d4d7a-fbb8-4123-a71d-70fa8caa1b7c","query":"sri ey","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:16:57.298Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"09703c2b-72f6-4488-b154-60333221e68f","query":"sri eye","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:16:57.725Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"0577002c-bbde-4e0f-a6a7-b796e9a1407a","count":20}
{"timestamp":"2025-07-17T15:16:59.492Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"ca400444-7692-437d-a00e-d3eb57393aeb","count":16}
{"timestamp":"2025-07-17T15:16:59.919Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"3475bbb9-e78f-4d83-b845-3783236961f5","query":"sri eye ","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:17:02.392Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"09703c2b-72f6-4488-b154-60333221e68f","count":11}
{"timestamp":"2025-07-17T15:17:02.803Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"3475bbb9-e78f-4d83-b845-3783236961f5","count":11}
{"timestamp":"2025-07-17T15:17:04.118Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"128d4d7a-fbb8-4123-a71d-70fa8caa1b7c","count":7}
{"timestamp":"2025-07-17T15:17:16.329Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"a91da48f-7220-422a-a870-f38d801168a6","query":"Ernst & Young","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:17:20.939Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"a91da48f-7220-422a-a870-f38d801168a6","count":10}
{"timestamp":"2025-07-17T15:17:30.439Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"b02ed045-a2ee-4bab-9610-ae1965fdb637","query":"sri","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:17:36.948Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"b02ed045-a2ee-4bab-9610-ae1965fdb637","count":20}
{"timestamp":"2025-07-17T15:17:41.584Z","level":"INFO","message":"Getting location suggestions from Local Falcon API","environment":"DEVELOPMENT","requestId":"7bb1e57d-2634-444a-8233-79d770468f06","query":"sri"}
{"timestamp":"2025-07-17T15:17:45.569Z","level":"INFO","message":"Local Falcon API response received","environment":"DEVELOPMENT","requestId":"7bb1e57d-2634-444a-8233-79d770468f06","count":5}
{"timestamp":"2025-07-17T15:19:12.919Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"d4820512-9ad8-466d-84b5-37f7c68bcc20","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:19:17.780Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"d4820512-9ad8-466d-84b5-37f7c68bcc20","count":1}
{"timestamp":"2025-07-17T15:19:58.900Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"70961f2a-2c55-4fe1-9326-7c6d166a2592","query":"sri","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:19:59.036Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"9c1710a2-939b-44da-8508-e14a09d630a1","query":"sri ","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:19:59.887Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"3efa741a-ae3c-40a4-b739-b2a40a413497","query":"sri e","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:20:00.035Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"7e0ae169-4f8c-492e-8b0c-9000e234fa61","query":"sri ey","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:20:00.201Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"d1a27a42-a75b-4995-ab6f-3a61ae2d3cd3","query":"sri eye","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:20:03.098Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"95991cf4-5c07-411d-a235-d081d594f5b7","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:20:03.465Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"9c1710a2-939b-44da-8508-e14a09d630a1","count":20}
{"timestamp":"2025-07-17T15:20:04.178Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"70961f2a-2c55-4fe1-9326-7c6d166a2592","count":20}
{"timestamp":"2025-07-17T15:20:04.275Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"7e0ae169-4f8c-492e-8b0c-9000e234fa61","count":7}
{"timestamp":"2025-07-17T15:20:05.590Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"3efa741a-ae3c-40a4-b739-b2a40a413497","count":16}
{"timestamp":"2025-07-17T15:20:05.948Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"d1a27a42-a75b-4995-ab6f-3a61ae2d3cd3","count":11}
{"timestamp":"2025-07-17T15:20:09.129Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"95991cf4-5c07-411d-a235-d081d594f5b7","count":1}
{"timestamp":"2025-07-17T15:23:46.160Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"3b4177f7-07ac-4436-8992-9dae4d3f5ce6","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:23:50.264Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"3b4177f7-07ac-4436-8992-9dae4d3f5ce6","count":1}
{"timestamp":"2025-07-17T15:23:50.317Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"a5b700ab-9131-4556-86b4-0761b82423cf","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:23:53.031Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"a5b700ab-9131-4556-86b4-0761b82423cf","count":1}
{"timestamp":"2025-07-17T15:23:53.099Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"20231f1b-04f9-4947-ba2e-6e717afa46b9","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:23:55.925Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"20231f1b-04f9-4947-ba2e-6e717afa46b9","count":1}
{"timestamp":"2025-07-17T15:23:55.974Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"6075316c-8d99-462a-9a22-e260bf2ebcb9","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:00.992Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"6075316c-8d99-462a-9a22-e260bf2ebcb9","count":1}
{"timestamp":"2025-07-17T15:24:01.057Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"154f06e6-67dc-41b0-a435-ca50b31ebad8","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:04.567Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"154f06e6-67dc-41b0-a435-ca50b31ebad8","count":1}
{"timestamp":"2025-07-17T15:24:04.643Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"60f080a4-1cc4-41f2-8885-12ae0f511af3","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:07.674Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"60f080a4-1cc4-41f2-8885-12ae0f511af3","count":1}
{"timestamp":"2025-07-17T15:24:07.725Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"f9dccc63-4b7b-4f0e-a6a8-f18a26c6a75e","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:11.330Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"f9dccc63-4b7b-4f0e-a6a8-f18a26c6a75e","count":1}
{"timestamp":"2025-07-17T15:24:11.385Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"57ed2175-19fe-4c27-bc68-93b837457be9","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:13.476Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"57ed2175-19fe-4c27-bc68-93b837457be9","count":1}
{"timestamp":"2025-07-17T15:24:13.524Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"42f98ea0-c204-4630-beab-b09aa2525005","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:17.143Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"ecd4b493-11d7-4f43-8ea6-44cb19004561","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:20.450Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"42f98ea0-c204-4630-beab-b09aa2525005","count":1}
{"timestamp":"2025-07-17T15:24:20.521Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"28119508-1984-4a73-9cec-059483395eb3","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:20.848Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"ecd4b493-11d7-4f43-8ea6-44cb19004561","count":1}
{"timestamp":"2025-07-17T15:24:20.938Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"84371052-2581-4a74-941c-56ef71603a58","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:23.559Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"84371052-2581-4a74-941c-56ef71603a58","count":1}
{"timestamp":"2025-07-17T15:24:23.615Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"1fd6b4f8-5804-4471-952f-81c58a0ce436","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:25.050Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"28119508-1984-4a73-9cec-059483395eb3","count":1}
{"timestamp":"2025-07-17T15:24:25.105Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"8682082c-db8e-4d39-9a82-a0688b0a7746","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:27.412Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"8682082c-db8e-4d39-9a82-a0688b0a7746","count":1}
{"timestamp":"2025-07-17T15:24:27.462Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"bf81cb64-d7e4-4895-95ae-e7c6c2484ab0","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:28.312Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"1fd6b4f8-5804-4471-952f-81c58a0ce436","count":1}
{"timestamp":"2025-07-17T15:24:28.354Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"b4c652ac-a90b-4525-bb59-2d060ad9922b","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:31.313Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"bf81cb64-d7e4-4895-95ae-e7c6c2484ab0","count":1}
{"timestamp":"2025-07-17T15:24:31.371Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"a2d1974a-96c9-4afe-8806-df2d0c1a62be","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:32.665Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"b4c652ac-a90b-4525-bb59-2d060ad9922b","count":1}
{"timestamp":"2025-07-17T15:24:32.744Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"7b8da995-6e58-4659-ae39-91cea5714bdf","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:35.253Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"a2d1974a-96c9-4afe-8806-df2d0c1a62be","count":1}
{"timestamp":"2025-07-17T15:24:35.327Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"a3d733b0-07fc-4d6a-ad96-01735cb86276","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:37.978Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"a3d733b0-07fc-4d6a-ad96-01735cb86276","count":1}
{"timestamp":"2025-07-17T15:24:38.130Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"0a3b8dd9-f500-445a-9893-d28a3d0dbd7b","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:39.407Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"7b8da995-6e58-4659-ae39-91cea5714bdf","count":1}
{"timestamp":"2025-07-17T15:24:39.536Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"232bb46a-8501-4e4c-bac9-c40593768651","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:40.857Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"0a3b8dd9-f500-445a-9893-d28a3d0dbd7b","count":1}
{"timestamp":"2025-07-17T15:24:41.035Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"0c6a449d-fb42-4915-a9f0-843bb805e005","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:41.958Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"232bb46a-8501-4e4c-bac9-c40593768651","count":1}
{"timestamp":"2025-07-17T15:24:42.280Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"70bccdf8-3c93-4626-8a7b-cc576e339217","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:44.781Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"70bccdf8-3c93-4626-8a7b-cc576e339217","count":1}
{"timestamp":"2025-07-17T15:24:44.855Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"d9728fdf-5103-495c-ae72-b9d6c9e84fde","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:47.223Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"0c6a449d-fb42-4915-a9f0-843bb805e005","count":1}
{"timestamp":"2025-07-17T15:24:47.369Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"cbedc3b9-98b1-43ac-a240-6311a0a361ec","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:47.405Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"d9728fdf-5103-495c-ae72-b9d6c9e84fde","count":1}
{"timestamp":"2025-07-17T15:24:47.491Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"88e5d0b7-5afd-4691-9edd-0b305b34fc3c","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:49.577Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"88e5d0b7-5afd-4691-9edd-0b305b34fc3c","count":1}
{"timestamp":"2025-07-17T15:24:49.634Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"94bee823-afc6-4fa9-b44b-2b074426e551","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:52.416Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"cbedc3b9-98b1-43ac-a240-6311a0a361ec","count":1}
{"timestamp":"2025-07-17T15:24:52.491Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"daff974d-a0fd-427a-b6d2-7447330a7e54","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:53.795Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"94bee823-afc6-4fa9-b44b-2b074426e551","count":1}
{"timestamp":"2025-07-17T15:24:53.855Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"c9795cac-a060-4403-9331-3965bc714f07","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:55.811Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"daff974d-a0fd-427a-b6d2-7447330a7e54","count":1}
{"timestamp":"2025-07-17T15:24:55.856Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"4fb03a33-0d0b-44ec-ab9a-c7fdf0bff51d","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:58.572Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"c9795cac-a060-4403-9331-3965bc714f07","count":1}
{"timestamp":"2025-07-17T15:24:58.688Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"4af3240d-873d-45e7-a2ac-b5d506f622c9","query":"Sri Udupi Food Hub Kammanahalli","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:24:59.417Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"4fb03a33-0d0b-44ec-ab9a-c7fdf0bff51d","count":1}
{"timestamp":"2025-07-17T15:25:01.343Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"4af3240d-873d-45e7-a2ac-b5d506f622c9","count":1}
{"timestamp":"2025-07-17T15:27:10.675Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"64e939f5-98bb-48ba-a53a-f7b53a413d44","query":"sri","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:27:11.181Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"7a1fb035-0f96-48cb-9cc8-46b7fa5d1bbc","query":"sri ","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:27:11.392Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"48c20bb6-ad21-4ab9-bc4e-b166aa481fa4","query":"sri e","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:27:12.878Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"18798a5f-3cb8-4666-a286-f89429e412e1","query":"sri ey","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:27:13.088Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"e37d1900-1b17-47b9-b878-bb50fc5d3bf4","query":"sri eye","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:27:14.929Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"48c20bb6-ad21-4ab9-bc4e-b166aa481fa4","count":16}
{"timestamp":"2025-07-17T15:27:15.166Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"7a1fb035-0f96-48cb-9cc8-46b7fa5d1bbc","count":20}
{"timestamp":"2025-07-17T15:27:16.068Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"64e939f5-98bb-48ba-a53a-f7b53a413d44","count":20}
{"timestamp":"2025-07-17T15:27:17.201Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"e37d1900-1b17-47b9-b878-bb50fc5d3bf4","count":11}
{"timestamp":"2025-07-17T15:27:17.679Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"18798a5f-3cb8-4666-a286-f89429e412e1","count":7}
{"timestamp":"2025-07-17T15:27:30.884Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"d1057f26-d8f4-404f-a214-3ff0fac7b7b8","query":"sri","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:27:31.070Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"844ef18e-dbbb-4d99-bf8f-d441b808cdde","query":"sri ","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:27:31.293Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"165a23d2-9bb7-4976-81a4-b272e3c9f5bc","query":"sri e","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:27:32.365Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"c1f31b9a-7c57-46c8-be61-9091e2dc69bf","query":"sri ey","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:27:32.574Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"a3eb2089-daef-4b43-942a-818e7f96f453","query":"sri eye","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:27:34.943Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"844ef18e-dbbb-4d99-bf8f-d441b808cdde","count":20}
{"timestamp":"2025-07-17T15:27:35.156Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"165a23d2-9bb7-4976-81a4-b272e3c9f5bc","count":16}
{"timestamp":"2025-07-17T15:27:36.502Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"d1057f26-d8f4-404f-a214-3ff0fac7b7b8","count":20}
{"timestamp":"2025-07-17T15:27:36.881Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"a3eb2089-daef-4b43-942a-818e7f96f453","count":11}
{"timestamp":"2025-07-17T15:27:37.326Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"c1f31b9a-7c57-46c8-be61-9091e2dc69bf","count":7}
{"timestamp":"2025-07-17T15:34:14.831Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"8009a277-316e-4757-9720-1b52f455c29b","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T15:34:14.838Z","level":"ERROR","message":"Error getting Local Falcon alerts:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:305:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:659:20)","userId":"52","unreadOnly":false}
{"timestamp":"2025-07-17T15:34:14.840Z","level":"ERROR","message":"Error getting alerts:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:305:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:659:20)","requestId":"8009a277-316e-4757-9720-1b52f455c29b"}
{"timestamp":"2025-07-17T15:34:14.847Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"e8d91fd7-5056-4215-bcae-923ff53ed2e1","userId":"52"}
{"timestamp":"2025-07-17T15:34:14.853Z","level":"ERROR","message":"Error getting Local Falcon configurations:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:88:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","userId":"52"}
{"timestamp":"2025-07-17T15:34:14.855Z","level":"ERROR","message":"Error getting configurations:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:88:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","requestId":"e8d91fd7-5056-4215-bcae-923ff53ed2e1"}
{"timestamp":"2025-07-17T15:34:26.934Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"3134a391-7844-4457-80c3-238f2631cc9d","query":"sri","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:34:29.163Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"5931af59-f8dd-4f7a-80fc-63c8b773aaaf","query":"sri ","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:34:30.550Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"3134a391-7844-4457-80c3-238f2631cc9d","count":20}
{"timestamp":"2025-07-17T15:34:30.651Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"9a811c3a-c2c2-43e4-9619-de7ccdeb87a6","query":"sri e","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:34:32.166Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"b0de42c4-d6bf-466a-9140-615ad38f799a","query":"sri eye ","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:34:32.621Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"9a811c3a-c2c2-43e4-9619-de7ccdeb87a6","count":0}
{"timestamp":"2025-07-17T15:34:33.628Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"5931af59-f8dd-4f7a-80fc-63c8b773aaaf","count":20}
{"timestamp":"2025-07-17T15:34:33.665Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"71fe5f53-b363-4a1a-a8ca-6fb74632f169","query":"sri eye c","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:34:35.175Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"6c1aff86-a101-4aa4-8eca-e47209ab2744","query":"sri eye ca","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:34:36.682Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"9d2f68fd-91b9-46a5-b5a0-c54f37f3fe19","query":"sri eye care","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:34:36.915Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"b0de42c4-d6bf-466a-9140-615ad38f799a","count":1}
{"timestamp":"2025-07-17T15:34:38.419Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"6c1aff86-a101-4aa4-8eca-e47209ab2744","count":1}
{"timestamp":"2025-07-17T15:34:38.702Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"1a8e7179-0ea5-4ada-94b8-cce7f2fde178","query":"Sri Eye Care Speciality Eye Hospital","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:34:40.850Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"71fe5f53-b363-4a1a-a8ca-6fb74632f169","count":1}
{"timestamp":"2025-07-17T15:34:42.316Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"1a8e7179-0ea5-4ada-94b8-cce7f2fde178","count":1}
{"timestamp":"2025-07-17T15:34:46.703Z","level":"ERROR","message":"Local Falcon places API call failed","environment":"DEVELOPMENT","requestId":"9d2f68fd-91b9-46a5-b5a0-c54f37f3fe19","error":"timeout of 10000ms exceeded","stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at searchPlaces (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:296:14)"}
{"timestamp":"2025-07-17T15:47:59.922Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"139f8b5b-2e9e-4b45-a6b2-00de9bd5ac45","query":"Sri Eye Care Speciality Eye Hospita","lat":40.7128,"lng":-74.006,"radius":1.2,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:48:01.766Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"6170cbc2-6ae2-4c55-adca-e0bba9395be0","query":"Sri Eye Care Speciality Eye Hospital","lat":40.7128,"lng":-74.006,"radius":1.2,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:48:04.563Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"139f8b5b-2e9e-4b45-a6b2-00de9bd5ac45","count":1}
{"timestamp":"2025-07-17T15:48:05.288Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"6170cbc2-6ae2-4c55-adca-e0bba9395be0","count":1}
{"timestamp":"2025-07-17T15:52:36.591Z","level":"INFO","message":"Saving Local Falcon configuration","environment":"DEVELOPMENT","requestId":"51956975-475a-4e90-8cd6-6f8a1cfccf59","userId":52,"name":"Sri Eye Care Conf"}
{"timestamp":"2025-07-17T15:52:36.597Z","level":"ERROR","message":"Error saving Local Falcon configuration:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.saveConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:52:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async saveConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:493:20)","configData":{"name":"Sri Eye Care Conf","keyword":"eye hospital","businessName":"Sri Eye Care Speciality Eye Hospital","placeId":"ChIJceJaLR8XrjsRLtW3QoeKOxc","centerLat":40.7128,"centerLng":-74.006,"gridSize":"3x3","radius":1.2,"unit":"kilometers","isScheduleEnabled":false,"settings":{},"userId":52}}
{"timestamp":"2025-07-17T15:52:36.598Z","level":"ERROR","message":"Error saving configuration:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.saveConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:52:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async saveConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:493:20)","requestId":"51956975-475a-4e90-8cd6-6f8a1cfccf59"}
{"timestamp":"2025-07-17T15:52:59.812Z","level":"INFO","message":"Saving Local Falcon configuration","environment":"DEVELOPMENT","requestId":"31f164f8-9363-469c-8b1f-d304d4ed6cf0","userId":52,"name":"Sri Eye Care Conf"}
{"timestamp":"2025-07-17T15:52:59.815Z","level":"ERROR","message":"Error saving Local Falcon configuration:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.saveConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:52:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async saveConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:493:20)","configData":{"name":"Sri Eye Care Conf","keyword":"eye hospital","businessName":"Sri Eye Care Speciality Eye Hospital","placeId":"ChIJceJaLR8XrjsRLtW3QoeKOxc","centerLat":40.7128,"centerLng":-74.006,"gridSize":"3x3","radius":1.2,"unit":"kilometers","isScheduleEnabled":false,"settings":{},"userId":52}}
{"timestamp":"2025-07-17T15:52:59.817Z","level":"ERROR","message":"Error saving configuration:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.saveConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:52:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async saveConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:493:20)","requestId":"31f164f8-9363-469c-8b1f-d304d4ed6cf0"}
{"timestamp":"2025-07-17T15:57:05.613Z","level":"INFO","message":"Saving Local Falcon configuration","environment":"DEVELOPMENT","requestId":"09dc2179-f4b9-447a-98b6-be9940de9d48","userId":52,"name":"Sri Eye Care Conf"}
{"timestamp":"2025-07-17T15:57:05.616Z","level":"ERROR","message":"Error saving Local Falcon configuration:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.saveConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:52:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async saveConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:493:20)","configData":{"name":"Sri Eye Care Conf","keyword":"eye hospital","businessName":"Sri Eye Care Speciality Eye Hospital","placeId":"ChIJceJaLR8XrjsRLtW3QoeKOxc","centerLat":40.7128,"centerLng":-74.006,"gridSize":"3x3","radius":1.2,"unit":"kilometers","isScheduleEnabled":false,"settings":{},"userId":52}}
{"timestamp":"2025-07-17T15:57:05.618Z","level":"ERROR","message":"Error saving configuration:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.saveConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:52:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async saveConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:493:20)","requestId":"09dc2179-f4b9-447a-98b6-be9940de9d48"}
{"timestamp":"2025-07-17T15:57:24.401Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"7e1f8953-c3e9-4b67-b9a8-8e922ac9335f","userId":"52"}
{"timestamp":"2025-07-17T15:57:24.405Z","level":"ERROR","message":"Error getting Local Falcon configurations:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:88:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","userId":"52"}
{"timestamp":"2025-07-17T15:57:24.407Z","level":"ERROR","message":"Error getting configurations:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:88:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","requestId":"7e1f8953-c3e9-4b67-b9a8-8e922ac9335f"}
{"timestamp":"2025-07-17T15:57:24.413Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"4ab65e4b-c94f-44d1-98ca-cd2a592da075","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T15:57:24.417Z","level":"ERROR","message":"Error getting Local Falcon alerts:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:305:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:659:20)","userId":"52","unreadOnly":false}
{"timestamp":"2025-07-17T15:57:24.423Z","level":"ERROR","message":"Error getting alerts:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:305:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:659:20)","requestId":"4ab65e4b-c94f-44d1-98ca-cd2a592da075"}
{"timestamp":"2025-07-17T15:57:36.089Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"a6cd4aa1-786d-4348-a5b9-c5aaa0f7e11c","query":"sri","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:57:38.140Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"846de6e5-e9d2-4ed6-b4fb-15719f84d427","query":"sri ","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:57:39.639Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"6fa8bd14-241f-4c2e-b8a4-3a74066a4c65","query":"sri e","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:57:40.373Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"a6cd4aa1-786d-4348-a5b9-c5aaa0f7e11c","count":5}
{"timestamp":"2025-07-17T15:57:44.323Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"846de6e5-e9d2-4ed6-b4fb-15719f84d427","count":5}
{"timestamp":"2025-07-17T15:57:45.720Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"6fa8bd14-241f-4c2e-b8a4-3a74066a4c65","count":0}
{"timestamp":"2025-07-17T15:57:46.334Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"6bbafd5b-a3f8-4462-8ce0-b7237e593e39","query":"sri ey","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:57:47.876Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"bdab05b6-8c82-483c-8ecc-bb23cf7ade22","query":"sri eye","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:57:50.705Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"01bae02b-9116-4ee0-9637-03fc59c9d5c0","query":"sri eye ","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:57:51.109Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"6bbafd5b-a3f8-4462-8ce0-b7237e593e39","count":1}
{"timestamp":"2025-07-17T15:57:51.673Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"bdab05b6-8c82-483c-8ecc-bb23cf7ade22","count":1}
{"timestamp":"2025-07-17T15:57:52.966Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"d415b25c-9c1e-4e64-a195-b5b289287469","query":"Sri Eye Care Speciality Eye Hospital","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T15:57:54.246Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"01bae02b-9116-4ee0-9637-03fc59c9d5c0","count":9}
{"timestamp":"2025-07-17T15:57:57.564Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"d415b25c-9c1e-4e64-a195-b5b289287469","count":1}
{"timestamp":"2025-07-17T15:58:00.612Z","level":"INFO","message":"Saving Local Falcon configuration","environment":"DEVELOPMENT","requestId":"27212426-3aff-42d3-b925-394a9a9ffdbb","userId":52,"name":"eye hospital"}
{"timestamp":"2025-07-17T15:58:00.615Z","level":"ERROR","message":"Error saving Local Falcon configuration:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.saveConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:52:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async saveConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:493:20)","configData":{"name":"eye hospital","keyword":"my restaurant","businessName":"Sri Eye Care Speciality Eye Hospital","placeId":"ChIJceJaLR8XrjsRLtW3QoeKOxc","centerLat":13.0196844,"centerLng":77.6285592,"gridSize":"5x5","radius":1,"unit":"kilometers","isScheduleEnabled":false,"settings":{},"userId":52}}
{"timestamp":"2025-07-17T15:58:00.617Z","level":"ERROR","message":"Error saving configuration:","environment":"DEVELOPMENT","error":"(intermediate value) is not iterable","stack":"TypeError: (intermediate value) is not iterable\n    at LocalFalcon.saveConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:52:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async saveConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:493:20)","requestId":"27212426-3aff-42d3-b925-394a9a9ffdbb"}
{"timestamp":"2025-07-17T16:07:00.511Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T16:07:00.528Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T16:07:00.564Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T10:37:02.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T16:14:37.700Z","level":"INFO","message":"Saving Local Falcon configuration","environment":"DEVELOPMENT","requestId":"10cd26df-83f3-4c17-8783-d1c922d9e4f0","userId":52,"name":"eye hospital"}
{"timestamp":"2025-07-17T16:14:37.705Z","level":"INFO","message":"Executing Local Falcon configuration save query","environment":"DEVELOPMENT","query":"INSERT INTO local_falcon_configurations (user_id, name, keyword, business_name, place_id, center_lat, center_lng, grid_size, radius, unit, is_schedule_enabled, schedule_frequency, alert_threshold, settings, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())","valuesCount":14,"values":[52,"eye hospital","my restaurant","Sri Eye Care Speciality Eye Hospital","ChIJceJaLR8XrjsRLtW3QoeKOxc",13.0196844,77.6285592,"5x5",1,"kilometers",false,null,null,"{}"]}
{"timestamp":"2025-07-17T16:14:37.756Z","level":"INFO","message":"Query executed successfully","environment":"DEVELOPMENT","resultType":"object","resultKeys":["fieldCount","affectedRows","insertId","info","serverStatus","warningStatus","changedRows"],"insertId":5}
{"timestamp":"2025-07-17T16:14:37.759Z","level":"INFO","message":"Local Falcon configuration saved successfully","environment":"DEVELOPMENT","configId":5,"userId":52,"name":"eye hospital"}
{"timestamp":"2025-07-17T16:14:37.804Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"bd06fe7a-9d1c-4821-9525-98e875649932","userId":"52"}
{"timestamp":"2025-07-17T16:14:37.849Z","level":"ERROR","message":"Error getting Local Falcon configurations:","environment":"DEVELOPMENT","error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:108:39\n    at Array.map (<anonymous>)\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:106:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","userId":"52"}
{"timestamp":"2025-07-17T16:14:37.852Z","level":"ERROR","message":"Error getting configurations:","environment":"DEVELOPMENT","error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:108:39\n    at Array.map (<anonymous>)\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:106:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","requestId":"bd06fe7a-9d1c-4821-9525-98e875649932"}
{"timestamp":"2025-07-17T16:14:58.084Z","level":"INFO","message":"Saving Local Falcon configuration","environment":"DEVELOPMENT","requestId":"3565eb64-e320-4539-9730-1d01fbacda29","userId":52,"name":"eye hospital"}
{"timestamp":"2025-07-17T16:14:58.086Z","level":"INFO","message":"Executing Local Falcon configuration save query","environment":"DEVELOPMENT","query":"INSERT INTO local_falcon_configurations (user_id, name, keyword, business_name, place_id, center_lat, center_lng, grid_size, radius, unit, is_schedule_enabled, schedule_frequency, alert_threshold, settings, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())","valuesCount":14,"values":[52,"eye hospital","my restaurant","Sri Eye Care Speciality Eye Hospital","ChIJceJaLR8XrjsRLtW3QoeKOxc",13.0196844,77.6285592,"5x5",1,"kilometers",false,null,null,"{}"]}
{"timestamp":"2025-07-17T16:14:58.133Z","level":"INFO","message":"Query executed successfully","environment":"DEVELOPMENT","resultType":"object","resultKeys":["fieldCount","affectedRows","insertId","info","serverStatus","warningStatus","changedRows"],"insertId":6}
{"timestamp":"2025-07-17T16:14:58.136Z","level":"INFO","message":"Local Falcon configuration saved successfully","environment":"DEVELOPMENT","configId":6,"userId":52,"name":"eye hospital"}
{"timestamp":"2025-07-17T16:14:58.168Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"583f2631-f0f2-42e0-acd6-d52733c4864a","userId":"52"}
{"timestamp":"2025-07-17T16:14:58.210Z","level":"ERROR","message":"Error getting Local Falcon configurations:","environment":"DEVELOPMENT","error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:108:39\n    at Array.map (<anonymous>)\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:106:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","userId":"52"}
{"timestamp":"2025-07-17T16:14:58.211Z","level":"ERROR","message":"Error getting configurations:","environment":"DEVELOPMENT","error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:108:39\n    at Array.map (<anonymous>)\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:106:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","requestId":"583f2631-f0f2-42e0-acd6-d52733c4864a"}
{"timestamp":"2025-07-17T16:15:18.838Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"4e1bbea4-8053-4b6d-ad20-5cbd655d2e84","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T16:15:18.843Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"09d51b11-0e92-4585-a504-feb6073d2481","userId":"52"}
{"timestamp":"2025-07-17T16:15:18.978Z","level":"ERROR","message":"Error getting Local Falcon configurations:","environment":"DEVELOPMENT","error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:108:39\n    at Array.map (<anonymous>)\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:106:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","userId":"52"}
{"timestamp":"2025-07-17T16:15:18.979Z","level":"ERROR","message":"Error getting configurations:","environment":"DEVELOPMENT","error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:108:39\n    at Array.map (<anonymous>)\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:106:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","requestId":"09d51b11-0e92-4585-a504-feb6073d2481"}
{"timestamp":"2025-07-17T16:15:29.064Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"a85253d3-7c5d-4d50-a3fc-40c26c1301a9","userId":"52"}
{"timestamp":"2025-07-17T16:15:29.070Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"b15dfa1b-8941-480c-866a-b14fd44e37bd","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T16:15:29.092Z","level":"ERROR","message":"Error getting Local Falcon configurations:","environment":"DEVELOPMENT","error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:108:39\n    at Array.map (<anonymous>)\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:106:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","userId":"52"}
{"timestamp":"2025-07-17T16:15:29.095Z","level":"ERROR","message":"Error getting configurations:","environment":"DEVELOPMENT","error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:108:39\n    at Array.map (<anonymous>)\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:106:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","requestId":"a85253d3-7c5d-4d50-a3fc-40c26c1301a9"}
{"timestamp":"2025-07-17T16:19:16.382Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"6501e3a3-b54e-43bc-bd01-151138d4f191","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T16:19:16.388Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"5d48605b-85bd-4ea5-a8e3-61b5c557f7b5","userId":"52"}
{"timestamp":"2025-07-17T16:19:16.423Z","level":"ERROR","message":"Error getting Local Falcon configurations:","environment":"DEVELOPMENT","error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:108:39\n    at Array.map (<anonymous>)\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:106:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","userId":"52"}
{"timestamp":"2025-07-17T16:19:16.427Z","level":"ERROR","message":"Error getting configurations:","environment":"DEVELOPMENT","error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:108:39\n    at Array.map (<anonymous>)\n    at LocalFalcon.getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\localFalcon.models.js:106:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:525:28)","requestId":"5d48605b-85bd-4ea5-a8e3-61b5c557f7b5"}
{"timestamp":"2025-07-17T16:19:43.875Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T16:19:43.890Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T16:19:43.937Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T10:49:45.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T16:20:02.382Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"2d59c72a-44e2-4d98-9ae2-d8a89d6f80a3","userId":"52"}
{"timestamp":"2025-07-17T16:20:02.389Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"bdcf8273-a91f-4b83-909e-98c64c1e46a5","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T16:20:02.522Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":6,"sampleRow":{"id":6,"name":"eye hospital","settingsType":"object","settingsValue":{}}}
{"timestamp":"2025-07-17T16:20:29.205Z","level":"INFO","message":"Deleting Local Falcon configuration","environment":"DEVELOPMENT","requestId":"152aac13-c34f-4174-a465-232efd6bb8a4","configId":"6"}
{"timestamp":"2025-07-17T16:20:29.304Z","level":"INFO","message":"Local Falcon configuration deleted successfully","environment":"DEVELOPMENT","configId":"6","affectedRows":1}
{"timestamp":"2025-07-17T16:20:29.337Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"df573aad-8dd0-42a6-a1bc-5ea055dcd3f0","userId":"52"}
{"timestamp":"2025-07-17T16:20:29.366Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":5,"sampleRow":{"id":5,"name":"eye hospital","settingsType":"object","settingsValue":{}}}
{"timestamp":"2025-07-17T16:20:31.707Z","level":"INFO","message":"Deleting Local Falcon configuration","environment":"DEVELOPMENT","requestId":"1d689734-ba0f-4f66-ab88-3d6207286911","configId":"5"}
{"timestamp":"2025-07-17T16:20:31.797Z","level":"INFO","message":"Local Falcon configuration deleted successfully","environment":"DEVELOPMENT","configId":"5","affectedRows":1}
{"timestamp":"2025-07-17T16:20:31.822Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"71f69119-127d-4f80-95e2-747e700c98e1","userId":"52"}
{"timestamp":"2025-07-17T16:20:31.851Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":4,"sampleRow":{"id":4,"name":"eye hospital","settingsType":"object","settingsValue":{}}}
{"timestamp":"2025-07-17T16:20:33.668Z","level":"INFO","message":"Deleting Local Falcon configuration","environment":"DEVELOPMENT","requestId":"955dd6c1-2f05-4ecb-8594-f55195a1f37b","configId":"4"}
{"timestamp":"2025-07-17T16:20:33.756Z","level":"INFO","message":"Local Falcon configuration deleted successfully","environment":"DEVELOPMENT","configId":"4","affectedRows":1}
{"timestamp":"2025-07-17T16:20:33.779Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"0fd66267-2e83-45bd-bf76-dc4df3814ef9","userId":"52"}
{"timestamp":"2025-07-17T16:20:33.809Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":3,"sampleRow":{"id":3,"name":"Sri Eye Care Conf","settingsType":"object","settingsValue":{}}}
{"timestamp":"2025-07-17T16:20:35.574Z","level":"INFO","message":"Deleting Local Falcon configuration","environment":"DEVELOPMENT","requestId":"05ee5ed1-e49c-494b-a114-4ec4c0944d27","configId":"3"}
{"timestamp":"2025-07-17T16:20:35.661Z","level":"INFO","message":"Local Falcon configuration deleted successfully","environment":"DEVELOPMENT","configId":"3","affectedRows":1}
{"timestamp":"2025-07-17T16:20:35.693Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"2cddcd16-ccf8-40e4-ad21-d6ebffd792fe","userId":"52"}
{"timestamp":"2025-07-17T16:20:35.720Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":2,"sampleRow":{"id":2,"name":"Sri Eye Care Conf","settingsType":"object","settingsValue":{}}}
{"timestamp":"2025-07-17T16:20:37.856Z","level":"INFO","message":"Deleting Local Falcon configuration","environment":"DEVELOPMENT","requestId":"e4455a86-a5c5-4f22-b993-e2593be124b6","configId":"2"}
{"timestamp":"2025-07-17T16:20:37.944Z","level":"INFO","message":"Local Falcon configuration deleted successfully","environment":"DEVELOPMENT","configId":"2","affectedRows":1}
{"timestamp":"2025-07-17T16:20:37.969Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"8d5de789-188e-4b4c-8089-ef7d7ec21093","userId":"52"}
{"timestamp":"2025-07-17T16:20:37.996Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":1,"sampleRow":{"id":1,"name":"Sri Eye Care Conf","settingsType":"object","settingsValue":{}}}
{"timestamp":"2025-07-17T16:20:40.980Z","level":"INFO","message":"Deleting Local Falcon configuration","environment":"DEVELOPMENT","requestId":"b4bd4306-f049-4e37-a2db-de0ac1c8d7d1","configId":"1"}
{"timestamp":"2025-07-17T16:20:41.067Z","level":"INFO","message":"Local Falcon configuration deleted successfully","environment":"DEVELOPMENT","configId":"1","affectedRows":1}
{"timestamp":"2025-07-17T16:20:41.097Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"f0e78602-7412-4c94-9e55-b2500c583f89","userId":"52"}
{"timestamp":"2025-07-17T16:20:41.124Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
{"timestamp":"2025-07-17T16:20:46.415Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"ef5e2156-6885-4f0a-9260-59d4ba9b7cca","userId":"52"}
{"timestamp":"2025-07-17T16:20:46.420Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"aa2b87c6-40c5-4484-8772-5b2f6fcf1aed","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T16:20:46.440Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
{"timestamp":"2025-07-17T16:21:00.763Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"cdb9d0fe-721f-40ce-bdbd-4c2c4681c526","query":"sri","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T16:21:06.540Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"cdb9d0fe-721f-40ce-bdbd-4c2c4681c526","count":10}
{"timestamp":"2025-07-17T16:21:06.648Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"82bb9691-e44a-4f20-9299-2dff71da9603","query":"sri ","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T16:21:08.855Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"b744f888-67a7-4775-a7ba-b30dbae1803d","query":"sri e","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T16:21:09.733Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"82bb9691-e44a-4f20-9299-2dff71da9603","count":5}
{"timestamp":"2025-07-17T16:21:10.345Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"8fdec466-6aac-4e6f-9922-8082862ded50","query":"sri eye","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T16:21:11.708Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"b744f888-67a7-4775-a7ba-b30dbae1803d","count":2}
{"timestamp":"2025-07-17T16:21:11.862Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"33efb0f0-f2f3-493e-8b09-2658af5734fc","query":"sri eye ","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T16:21:12.608Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"8fdec466-6aac-4e6f-9922-8082862ded50","count":1}
{"timestamp":"2025-07-17T16:21:13.358Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"0c9f7ff9-60d3-44b7-bbb0-7b05fbb59d49","query":"sri eye c","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T16:21:14.864Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"7d9b8733-057c-4a51-8562-ab9aea087c09","query":"sri eye ca","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T16:21:15.817Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"33efb0f0-f2f3-493e-8b09-2658af5734fc","count":1}
{"timestamp":"2025-07-17T16:21:16.186Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"0c9f7ff9-60d3-44b7-bbb0-7b05fbb59d49","count":10}
{"timestamp":"2025-07-17T16:21:18.067Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"7d9b8733-057c-4a51-8562-ab9aea087c09","count":1}
{"timestamp":"2025-07-17T16:21:18.665Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"a8af061a-517e-497b-a386-0b92d749ec3d","query":"Sri Eye Care Speciality Eye Hospital","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T16:21:21.631Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"a8af061a-517e-497b-a386-0b92d749ec3d","count":1}
{"timestamp":"2025-07-17T16:21:24.094Z","level":"INFO","message":"Running grid scan","environment":"DEVELOPMENT","requestId":"1f9d167f-ec8f-4bcf-a577-b610cc02aa72","keyword":"eye hospital","businessName":"Sri Eye Care Speciality Eye Hospital","lat":13.0196844,"lng":77.6285592,"gridSize":"5x5","radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T16:21:24.095Z","level":"ERROR","message":"Grid scan functionality not implemented with Local Falcon API","environment":"DEVELOPMENT","requestId":"1f9d167f-ec8f-4bcf-a577-b610cc02aa72","stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at runGridScan (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:426:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at isAuthenticated (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\middleware\\isAuthenticated.js:10:14)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\express\\lib\\router\\index.js:284:15"}
{"timestamp":"2025-07-17T16:27:09.187Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T16:27:09.206Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T16:27:09.251Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T10:57:10.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T16:27:19.359Z","level":"INFO","message":"Running grid scan","environment":"DEVELOPMENT","requestId":"8f872cf7-1e50-44a9-bbd1-4d3c77f6a10b","keyword":"eye hospital","businessName":"Sri Eye Care Speciality Eye Hospital","lat":13.0196844,"lng":77.6285592,"gridSize":"5x5","radius":1,"unit":"kilometers","placeId":"ChIJceJaLR8XrjsRLtW3QoeKOxc"}
{"timestamp":"2025-07-17T16:27:19.362Z","level":"INFO","message":"Calling Local Falcon scan API","environment":"DEVELOPMENT","requestId":"8f872cf7-1e50-44a9-bbd1-4d3c77f6a10b","endpoint":"https://api.localfalcon.com/v1/scan/","parameters":{"keyword":"eye hospital","lat":13.0196844,"lng":77.6285592,"grid_size":"5x5","radius":1,"measurement":"km","place_id":"ChIJceJaLR8XrjsRLtW3QoeKOxc"}}
{"timestamp":"2025-07-17T16:27:21.223Z","level":"ERROR","message":"Error running grid scan:","environment":"DEVELOPMENT","error":"Request failed with status code 400","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:526:35)\n    at IncomingMessage.emit (node:domain:488:12)\n    at endReadableNT (node:internal/streams/readable:1408:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async runGridScan (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:474:22)","requestId":"8f872cf7-1e50-44a9-bbd1-4d3c77f6a10b","isAxiosError":true,"response":{"status":400,"data":{"code":400,"code_desc":false,"success":false,"message":"You must specify a valid grid size (3,5,7,9,11,13,15,17,19,21).","parameters":{"keyword":"eye hospital","lat":"13.0196844","lng":"77.6285592","grid_size":"5x5","radius":"1","measurement":"km","place_id":"ChIJceJaLR8XrjsRLtW3QoeKOxc"},"data":[]}}}
{"timestamp":"2025-07-17T16:27:36.727Z","level":"INFO","message":"Running grid scan","environment":"DEVELOPMENT","requestId":"08cfbb5f-0cc0-4b37-870b-346315f7ce1e","keyword":"eye hospital","businessName":"Sri Eye Care Speciality Eye Hospital","lat":13.0196844,"lng":77.6285592,"gridSize":"5x5","radius":1,"unit":"kilometers","placeId":"ChIJceJaLR8XrjsRLtW3QoeKOxc"}
{"timestamp":"2025-07-17T16:27:36.728Z","level":"INFO","message":"Calling Local Falcon scan API","environment":"DEVELOPMENT","requestId":"08cfbb5f-0cc0-4b37-870b-346315f7ce1e","endpoint":"https://api.localfalcon.com/v1/scan/","parameters":{"keyword":"eye hospital","lat":13.0196844,"lng":77.6285592,"grid_size":"5x5","radius":1,"measurement":"km","place_id":"ChIJceJaLR8XrjsRLtW3QoeKOxc"}}
{"timestamp":"2025-07-17T16:27:38.508Z","level":"ERROR","message":"Error running grid scan:","environment":"DEVELOPMENT","error":"Request failed with status code 400","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:526:35)\n    at IncomingMessage.emit (node:domain:488:12)\n    at endReadableNT (node:internal/streams/readable:1408:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async runGridScan (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:474:22)","requestId":"08cfbb5f-0cc0-4b37-870b-346315f7ce1e","isAxiosError":true,"response":{"status":400,"data":{"code":400,"code_desc":false,"success":false,"message":"You must specify a valid grid size (3,5,7,9,11,13,15,17,19,21).","parameters":{"keyword":"eye hospital","lat":"13.0196844","lng":"77.6285592","grid_size":"5x5","radius":"1","measurement":"km","place_id":"ChIJceJaLR8XrjsRLtW3QoeKOxc"},"data":[]}}}
{"timestamp":"2025-07-17T16:29:31.310Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T16:29:31.322Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T16:29:31.373Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T10:59:32.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T16:29:34.483Z","level":"INFO","message":"Running grid scan","environment":"DEVELOPMENT","requestId":"385d1587-0a6e-446d-a18e-84990a9a14b9","keyword":"eye hospital","businessName":"Sri Eye Care Speciality Eye Hospital","lat":13.0196844,"lng":77.6285592,"gridSize":"5x5","radius":1,"unit":"kilometers","placeId":"ChIJceJaLR8XrjsRLtW3QoeKOxc"}
{"timestamp":"2025-07-17T16:29:34.486Z","level":"INFO","message":"Calling Local Falcon scan API","environment":"DEVELOPMENT","requestId":"385d1587-0a6e-446d-a18e-84990a9a14b9","endpoint":"https://api.localfalcon.com/v1/scan/","parameters":{"keyword":"eye hospital","lat":13.0196844,"lng":77.6285592,"grid_size":"5","original_grid_size":"5x5","radius":1,"measurement":"km","original_unit":"kilometers","place_id":"ChIJceJaLR8XrjsRLtW3QoeKOxc"}}
{"timestamp":"2025-07-17T16:29:54.166Z","level":"INFO","message":"Local Falcon scan API response","environment":"DEVELOPMENT","requestId":"385d1587-0a6e-446d-a18e-84990a9a14b9","status":200,"dataKeys":["code","code_desc","success","message","parameters","data"]}
{"timestamp":"2025-07-17T16:30:24.460Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"77501007-b5fe-49e0-9973-db7e37ad00ce","query":"Sri Eye Care Speciality Eye Hospital","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T16:30:29.070Z","level":"INFO","message":"Running grid scan","environment":"DEVELOPMENT","requestId":"9862b661-0413-454a-aa7d-34f61443d0e1","keyword":"eye hospital","businessName":"Sri Eye Care Speciality Eye Hospital","lat":13.0196844,"lng":77.6285592,"gridSize":"5x5","radius":1,"unit":"kilometers","placeId":"ChIJceJaLR8XrjsRLtW3QoeKOxc"}
{"timestamp":"2025-07-17T16:30:29.072Z","level":"INFO","message":"Calling Local Falcon scan API","environment":"DEVELOPMENT","requestId":"9862b661-0413-454a-aa7d-34f61443d0e1","endpoint":"https://api.localfalcon.com/v1/scan/","parameters":{"keyword":"eye hospital","lat":13.0196844,"lng":77.6285592,"grid_size":"5","original_grid_size":"5x5","radius":1,"measurement":"km","original_unit":"kilometers","place_id":"ChIJceJaLR8XrjsRLtW3QoeKOxc"}}
{"timestamp":"2025-07-17T16:30:34.502Z","level":"ERROR","message":"Local Falcon places API call failed","environment":"DEVELOPMENT","requestId":"77501007-b5fe-49e0-9973-db7e37ad00ce","error":"timeout of 10000ms exceeded","stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at searchPlaces (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\localFalcon.controller.js:296:14)"}
{"timestamp":"2025-07-17T16:30:43.237Z","level":"INFO","message":"Local Falcon scan API response","environment":"DEVELOPMENT","requestId":"9862b661-0413-454a-aa7d-34f61443d0e1","status":200,"dataKeys":["code","code_desc","success","message","parameters","data"]}
{"timestamp":"2025-07-17T16:48:16.288Z","level":"INFO","message":"Running grid scan","environment":"DEVELOPMENT","requestId":"27df31df-d375-4534-9d77-84c773a6eb6b","keyword":"eye hospital","businessName":"Sri Eye Care Speciality Eye Hospital","lat":13.0196844,"lng":77.6285592,"gridSize":"3x3","radius":1,"unit":"kilometers","placeId":"ChIJceJaLR8XrjsRLtW3QoeKOxc"}
{"timestamp":"2025-07-17T16:48:16.296Z","level":"INFO","message":"Calling Local Falcon scan API","environment":"DEVELOPMENT","requestId":"27df31df-d375-4534-9d77-84c773a6eb6b","endpoint":"https://api.localfalcon.com/v1/scan/","parameters":{"keyword":"eye hospital","lat":13.0196844,"lng":77.6285592,"grid_size":"3","original_grid_size":"3x3","radius":1,"measurement":"km","original_unit":"kilometers","place_id":"ChIJceJaLR8XrjsRLtW3QoeKOxc"}}
{"timestamp":"2025-07-17T16:48:24.164Z","level":"INFO","message":"Local Falcon scan API response","environment":"DEVELOPMENT","requestId":"27df31df-d375-4534-9d77-84c773a6eb6b","status":200,"dataKeys":["code","code_desc","success","message","parameters","data"]}
{"timestamp":"2025-07-17T16:49:33.472Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"b043ce75-7b22-4c9d-a644-a8358bdc36ca","query":"Sri Eye Care Speciality Eye Hospital","lat":13.0196844,"lng":77.6285592,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-17T16:49:38.228Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"b043ce75-7b22-4c9d-a644-a8358bdc36ca","count":1}
{"timestamp":"2025-07-17T17:01:51.653Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"5070d828-fb19-4de1-9a9f-f9027d7c347e","userId":"52"}
{"timestamp":"2025-07-17T17:01:51.679Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"a88341d9-17d3-470e-bced-ed0e29ccf290","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T17:01:51.732Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
{"timestamp":"2025-07-17T17:01:55.078Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f7d117ea-3eae-4f3a-bc02-f143c43a8d24","controller":"business","action":"businessList","userId":"132"}
{"timestamp":"2025-07-17T17:01:55.080Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"f7d117ea-3eae-4f3a-bc02-f143c43a8d24","userId":"132"}
{"timestamp":"2025-07-17T17:01:55.142Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"f7d117ea-3eae-4f3a-bc02-f143c43a8d24","userId":"132","businessCount":1}
{"timestamp":"2025-07-17T17:01:55.288Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ad338b72-c748-4453-9642-************","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-07-17T17:04:43.907Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"478da148-83b8-4bd3-8a2b-d2b2d3dd47d6","userId":"52"}
{"timestamp":"2025-07-17T17:04:43.913Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"d56cc2c6-cd85-4339-9e09-b8fc7d3c82d3","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T17:04:43.933Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
{"timestamp":"2025-07-17T17:04:56.096Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"1b260db6-b63a-4d37-ad57-986620a72a28","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T17:04:56.101Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"aa628815-7f42-4f52-be74-79f7c4d57752","userId":"52"}
{"timestamp":"2025-07-17T17:04:56.126Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
{"timestamp":"2025-07-17T17:05:14.820Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"b334d296-ee04-407b-82c4-30064716eebf","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T17:05:14.824Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"922707e6-5312-4c1b-83b2-89b1b11bff53","userId":"52"}
{"timestamp":"2025-07-17T17:05:14.853Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
{"timestamp":"2025-07-17T17:05:31.455Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"89bf52b9-86f1-45a9-b84f-78d1ea0abb86","userId":"52"}
{"timestamp":"2025-07-17T17:05:31.460Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"1d2f054d-d2f7-487e-9017-9519110109bb","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T17:05:31.482Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
{"timestamp":"2025-07-17T17:08:27.691Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"18c5ea51-8c2b-410c-b242-d00a57f9a7c3","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T17:08:27.697Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"97ff591f-e770-4784-b171-37a96cbb536c","userId":"52"}
{"timestamp":"2025-07-17T17:08:27.723Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
{"timestamp":"2025-07-17T17:08:36.916Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"257eaf2f-1817-40ef-9fff-b929500d45ae","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T17:08:36.919Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"56f8bfa7-041f-45cf-80ce-eb6f2d57e111","userId":"52"}
{"timestamp":"2025-07-17T17:08:36.945Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
{"timestamp":"2025-07-17T17:12:45.157Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"b37c5af5-aa2f-4658-acab-cd887ca55b83","userId":"52"}
{"timestamp":"2025-07-17T17:12:45.164Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"142bed6f-c6d8-48f2-9a20-ad3be5bc8781","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T17:12:45.185Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
{"timestamp":"2025-07-17T17:16:02.135Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"59cbb280-4834-4802-9adf-6a85cfcd184e","userId":"52"}
{"timestamp":"2025-07-17T17:16:02.144Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"7fdc6bb0-c7c1-48d2-93fc-0f2cfca76868","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T17:16:02.166Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
{"timestamp":"2025-07-17T17:19:21.039Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"622afccc-1f80-4250-8876-e6a21a1801c8","userId":"52"}
{"timestamp":"2025-07-17T17:19:21.050Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"021645a5-027c-48fd-bea9-eebbd10edce8","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T17:19:21.076Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
{"timestamp":"2025-07-17T19:07:30.456Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-17T19:07:30.477Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-17T19:07:30.550Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-17T13:37:32.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-17T19:09:26.580Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"fa466df8-2319-499c-8b95-6eb5d593b35a","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-07-17T19:09:26.583Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"fa466df8-2319-499c-8b95-6eb5d593b35a","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-07-17T19:09:26.922Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"fa466df8-2319-499c-8b95-6eb5d593b35a","userId":52,"email":"<EMAIL>"}
{"timestamp":"2025-07-17T19:17:57.101Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"b27291de-0ffe-4e37-9de3-31241fe91538","userId":"52"}
{"timestamp":"2025-07-17T19:17:57.115Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"a146b58d-d934-4864-b6fe-67ab70fbbaaf","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T19:17:57.146Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
{"timestamp":"2025-07-17T19:20:27.195Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"7627e1b7-d099-469c-9c72-891293d4328a","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-07-17T19:20:27.197Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"7627e1b7-d099-469c-9c72-891293d4328a","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-07-17T19:20:27.371Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"7627e1b7-d099-469c-9c72-891293d4328a","userId":52,"email":"<EMAIL>"}
{"timestamp":"2025-07-17T19:20:32.031Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"ef30d284-59d9-47ad-bd70-a1aa4e83aa42","userId":"52"}
{"timestamp":"2025-07-17T19:20:32.038Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"4bba102c-aa9d-4a89-bf9f-77d3ea313cc4","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T19:20:32.068Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
{"timestamp":"2025-07-17T19:24:46.253Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"bff79ecc-ee07-4a35-9c5c-001869cc0fc0","userId":"52"}
{"timestamp":"2025-07-17T19:24:46.266Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"b1a6d9e4-bee6-42dd-9be1-68fbd8a3d3d8","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T19:24:46.292Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
{"timestamp":"2025-07-17T19:34:59.246Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"49dc2212-2238-4f5e-89d1-a97f0b6adcca","userId":"52"}
{"timestamp":"2025-07-17T19:34:59.257Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"4d9b1ba6-2046-4990-b044-77b90b9f8831","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T19:34:59.283Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
{"timestamp":"2025-07-17T19:35:14.264Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"1f23257e-23d2-4aba-8613-4f246c265822","userId":"52"}
{"timestamp":"2025-07-17T19:35:14.272Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"cd3c4320-1dc4-41cc-8306-e18ffd265d0c","userId":"52","unreadOnly":"false"}
{"timestamp":"2025-07-17T19:35:14.301Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"52","rowCount":0,"sampleRow":null}
