# Local Falcon Integration for GMB Social Platform

## Overview

This document outlines the integration of Local Falcon-like functionality into the existing GMB Social platform's Geo Grid feature. The integration will provide local search rank tracking, heat map visualizations, and competitor analysis capabilities.

## Features to Implement

### 1. Local Search Rank Tracking
- Track business rankings across grid points
- Support multiple search terms per grid
- Real-time and scheduled ranking checks
- Historical ranking data storage

### 2. Heat Map Visualization
- Color-coded grid points based on ranking positions
- Interactive heat map overlays on the existing map component
- Ranking trend indicators
- Competitor comparison views

### 3. Automated Monitoring
- Scheduled ranking checks (hourly, daily, weekly)
- Email/SMS alerts for ranking changes
- Performance threshold notifications
- Ranking drop alerts

### 4. Reporting & Analytics
- Ranking performance reports
- Competitor analysis dashboards
- Historical trend charts
- Export capabilities (PDF, CSV, Excel)

## Technical Implementation

### Frontend Changes

#### 1. Enhanced Data Models

```typescript
// Add to geoGrid.service.tsx
interface RankingData {
  id?: number;
  gridPointId: string;
  businessId: string;
  searchTerm: string;
  position: number | null;
  isVisible: boolean;
  competitorData?: CompetitorRanking[];
  timestamp: Date;
  url?: string;
  snippet?: string;
}

interface CompetitorRanking {
  businessName: string;
  position: number;
  businessId?: string;
  rating?: number;
  reviewCount?: number;
}

interface LocalFalconConfig {
  searchTerms: string[];
  trackingEnabled: boolean;
  checkInterval: number; // minutes
  businessesToTrack: string[];
  competitorsToTrack: string[];
  alertThresholds: {
    positionDrop: number;
    visibilityLoss: boolean;
  };
}

interface HeatMapSettings {
  colorScheme: 'green-red' | 'blue-red' | 'custom';
  showCompetitors: boolean;
  showOnlyVisible: boolean;
  positionRange: [number, number];
}
```

#### 2. Component Updates

**GeoGridScreen Component:**
```typescript
// Additional state management
const [rankingData, setRankingData] = useState<RankingData[]>([]);
const [localFalconConfig, setLocalFalconConfig] = useState<LocalFalconConfig>();
const [heatMapSettings, setHeatMapSettings] = useState<HeatMapSettings>();
const [competitorData, setCompetitorData] = useState<CompetitorRanking[]>([]);
const [rankingHistory, setRankingHistory] = useState<RankingData[]>([]);

// New functions
const checkRankingsAtGridPoints = async () => { /* Implementation */ };
const scheduleRankingChecks = async () => { /* Implementation */ };
const generateRankingReport = async () => { /* Implementation */ };
const exportRankingData = async (format: 'pdf' | 'csv' | 'excel') => { /* Implementation */ };
```

**New Components to Create:**

1. **LocalFalconControls.component.tsx**
   - Search term management
   - Tracking configuration
   - Competitor setup
   - Alert settings

2. **RankingHeatMap.component.tsx**
   - Heat map overlay for the existing map
   - Color-coded grid points
   - Interactive ranking tooltips
   - Legend and controls

3. **RankingDashboard.component.tsx**
   - Ranking performance charts
   - Competitor comparison tables
   - Historical trend graphs
   - Alert management

4. **RankingReports.component.tsx**
   - Report generation interface
   - Export options
   - Scheduled report settings
   - Email delivery configuration

#### 3. Enhanced Map Integration

**GeoGridMap Component Updates:**
```typescript
interface GeoGridMapProps {
  // Existing props...
  rankingData?: RankingData[];
  heatMapSettings?: HeatMapSettings;
  showHeatMap?: boolean;
  onGridPointClick?: (point: GridPoint, rankings: RankingData[]) => void;
}

// Heat map color calculation
const getHeatMapColor = (position: number | null): string => {
  if (!position) return '#cccccc'; // Not visible
  if (position <= 3) return '#00ff00'; // Top 3 - Green
  if (position <= 10) return '#ffff00'; // Top 10 - Yellow
  if (position <= 20) return '#ff8800'; // Top 20 - Orange
  return '#ff0000'; // Below 20 - Red
};
```

### Backend Changes

#### 1. Database Schema Updates

```sql
-- Ranking data table
CREATE TABLE local_rankings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  grid_configuration_id INT,
  grid_point_index INT,
  business_id VARCHAR(255),
  search_term VARCHAR(255),
  position INT NULL,
  is_visible BOOLEAN DEFAULT FALSE,
  competitor_data JSON,
  url TEXT,
  snippet TEXT,
  check_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (grid_configuration_id) REFERENCES geo_grid_configurations(id)
);

-- Ranking check schedules
CREATE TABLE ranking_schedules (
  id INT PRIMARY KEY AUTO_INCREMENT,
  grid_configuration_id INT,
  search_terms JSON,
  check_interval INT, -- minutes
  is_active BOOLEAN DEFAULT TRUE,
  last_check TIMESTAMP,
  next_check TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (grid_configuration_id) REFERENCES geo_grid_configurations(id)
);

-- Competitor tracking
CREATE TABLE competitor_businesses (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT,
  competitor_name VARCHAR(255),
  business_id VARCHAR(255),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Ranking alerts
CREATE TABLE ranking_alerts (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT,
  grid_configuration_id INT,
  alert_type ENUM('position_drop', 'visibility_loss', 'competitor_gain'),
  threshold_value INT,
  is_active BOOLEAN DEFAULT TRUE,
  notification_methods JSON, -- email, sms, webhook
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. New API Endpoints

**Ranking Controller (rankingController.js):**
```javascript
// POST /api/v1/rankings/check
exports.checkRankings = async (req, res) => {
  // Check rankings for specific grid points and search terms
};

// GET /api/v1/rankings/grid/:configId
exports.getGridRankings = async (req, res) => {
  // Get all ranking data for a grid configuration
};

// POST /api/v1/rankings/schedule
exports.scheduleRankingChecks = async (req, res) => {
  // Set up automated ranking checks
};

// GET /api/v1/rankings/history/:configId
exports.getRankingHistory = async (req, res) => {
  // Get historical ranking data
};

// POST /api/v1/rankings/export
exports.exportRankingData = async (req, res) => {
  // Export ranking data in various formats
};

// POST /api/v1/competitors
exports.addCompetitor = async (req, res) => {
  // Add competitor for tracking
};

// GET /api/v1/competitors/:userId
exports.getCompetitors = async (req, res) => {
  // Get user's competitor list
};
```

#### 3. Local Search Integration

**Google My Business API Integration:**
```javascript
// services/localSearchService.js
class LocalSearchService {
  async checkLocalRanking(lat, lng, searchTerm, businessName) {
    // Use Google Places API or custom scraping
    // Return ranking position and competitor data
  }

  async getLocalSearchResults(lat, lng, searchTerm, radius = 1000) {
    // Get all local search results for analysis
  }

  async trackCompetitorRankings(lat, lng, searchTerm, competitors) {
    // Track multiple competitors simultaneously
  }
}
```

#### 4. Automated Ranking Checks

**Cron Job Implementation:**
```javascript
// jobs/rankingCheckJob.js
const cron = require('node-cron');

// Run every hour to check scheduled ranking updates
cron.schedule('0 * * * *', async () => {
  const scheduledChecks = await getRankingSchedules();
  
  for (const schedule of scheduledChecks) {
    if (shouldRunCheck(schedule)) {
      await performRankingCheck(schedule);
    }
  }
});
```

### Integration Steps

#### Phase 1: Basic Ranking Tracking
1. Create database tables for ranking data
2. Implement basic ranking check API
3. Add ranking visualization to existing map
4. Create simple ranking configuration UI

#### Phase 2: Advanced Features
1. Implement competitor tracking
2. Add heat map visualizations
3. Create ranking history and trends
4. Build alert system

#### Phase 3: Automation & Reporting
1. Implement scheduled ranking checks
2. Create comprehensive reporting dashboard
3. Add export functionality
4. Build email/SMS notification system

#### Phase 4: Enterprise Features
1. White-label reporting
2. API access for third-party integrations
3. Advanced analytics and insights
4. Multi-location management

## Configuration

### Environment Variables

Add to `.env.development` and `.env.production`:

```env
# Local Falcon Integration
GOOGLE_PLACES_API_KEY=your_google_places_api_key
RANKING_CHECK_INTERVAL=60
MAX_CONCURRENT_CHECKS=10
RANKING_DATA_RETENTION_DAYS=365

# Email notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# SMS notifications (optional)
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE_NUMBER=your_twilio_number
```

### Logging Configuration

Update `config/logging.config.js`:

```javascript
logFiles: {
  // ... existing files
  ranking: 'ranking',     // Ranking check logs
  alerts: 'alerts'        // Alert system logs
},

// Ranking logging settings
ranking: {
  logChecks: true,
  logFailures: true,
  logPerformance: true,
  logCompetitorData: process.env.APP_LOG_LEVEL === 'DEBUG'
}
```

## Usage Examples

### 1. Basic Ranking Check

```typescript
// Check rankings for current grid
const checkRankings = async () => {
  const config = {
    searchTerms: ['restaurant near me', 'pizza delivery'],
    businessesToTrack: ['My Restaurant'],
    competitorsToTrack: ['Competitor A', 'Competitor B']
  };
  
  await geoGridService.checkRankings(gridConfiguration.id, config);
};
```

### 2. Schedule Automated Checks

```typescript
// Set up daily ranking checks
const scheduleChecks = async () => {
  await geoGridService.scheduleRankingChecks({
    configId: gridConfiguration.id,
    interval: 1440, // 24 hours
    searchTerms: ['restaurant near me'],
    alertOnDrop: true
  });
};
```

### 3. Generate Heat Map

```typescript
// Display ranking heat map
<RankingHeatMap
  gridPoints={gridPoints}
  rankingData={rankingData}
  settings={{
    colorScheme: 'green-red',
    showCompetitors: true,
    positionRange: [1, 20]
  }}
/>
```

## Benefits

### For Businesses
- **Visibility Tracking**: Monitor local search visibility across different areas
- **Competitor Analysis**: Track competitor performance in local search
- **Performance Optimization**: Identify areas for local SEO improvement
- **ROI Measurement**: Measure the impact of local marketing efforts

### For Agencies
- **Client Reporting**: Automated ranking reports for clients
- **Multi-location Management**: Track rankings across multiple client locations
- **Competitive Intelligence**: Monitor competitor strategies and performance
- **Scalable Monitoring**: Automated checks across hundreds of locations

## Future Enhancements

1. **AI-Powered Insights**: Machine learning for ranking prediction and optimization suggestions
2. **Voice Search Tracking**: Monitor voice search rankings
3. **Mobile vs Desktop**: Separate tracking for mobile and desktop results
4. **Local Pack Monitoring**: Specific tracking for Google Local Pack results
5. **Review Impact Analysis**: Correlate review changes with ranking fluctuations
6. **Social Media Integration**: Track social media mentions and their impact on local rankings

## Cost Considerations

- Google Places API usage costs
- Additional server resources for automated checks
- Storage costs for historical ranking data
- Third-party service integrations (email, SMS)

## Security & Compliance

- Rate limiting for API calls to prevent abuse
- Secure storage of competitor data
- GDPR compliance for data retention
- API key security and rotation

This integration will transform the existing Geo Grid feature into a comprehensive local SEO tracking and analysis platform, similar to Local Falcon but integrated directly into your GMB Social ecosystem.