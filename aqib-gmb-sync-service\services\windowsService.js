const Service = require('node-windows').Service;
const path = require('path');
const logger = require('../utils/logger');

class WindowsServiceManager {
  constructor() {
    this.service = null;
    this.serviceName = process.env.SERVICE_NAME || 'GMB-AutoReply-Service';
    this.serviceDescription = process.env.SERVICE_DESCRIPTION || 'Automated Google My Business Review Reply Service';
    this.scriptPath = path.join(__dirname, '../app.js');
  }

  /**
   * Create and configure the Windows service
   */
  createService() {
    try {
      this.service = new Service({
        name: this.serviceName,
        description: this.serviceDescription,
        script: this.scriptPath,
        nodeOptions: [
          '--max_old_space_size=4096'
        ],
        env: [
          {
            name: "NODE_ENV",
            value: process.env.NODE_ENV || "production"
          },
          {
            name: "LOG_LEVEL",
            value: process.env.LOG_LEVEL || "info"
          }
        ],
        workingDirectory: path.dirname(this.scriptPath),
        allowServiceLogon: true
      });

      this.setupEventHandlers();

      logger.logService('SERVICE_CREATED', {
        serviceName: this.serviceName,
        scriptPath: this.scriptPath,
        description: this.serviceDescription
      });

      return this.service;

    } catch (error) {
      logger.error('Error creating Windows service:', error);
      throw error;
    }
  }

  /**
   * Setup event handlers for the service
   */
  setupEventHandlers() {
    if (!this.service) {
      throw new Error('Service not created. Call createService() first.');
    }

    // Service installation events
    this.service.on('install', () => {
      logger.logService('INSTALLED', {
        serviceName: this.serviceName,
        message: 'Service installed successfully'
      });
      console.log(`${this.serviceName} installed successfully.`);
    });

    this.service.on('alreadyinstalled', () => {
      logger.logService('ALREADY_INSTALLED', {
        serviceName: this.serviceName,
        message: 'Service is already installed'
      });
      console.log(`${this.serviceName} is already installed.`);
    });

    this.service.on('invalidinstallation', () => {
      logger.logService('INVALID_INSTALLATION', {
        serviceName: this.serviceName,
        message: 'Invalid installation detected'
      });
      console.log(`${this.serviceName} installation is invalid.`);
    });

    // Service uninstallation events
    this.service.on('uninstall', () => {
      logger.logService('UNINSTALLED', {
        serviceName: this.serviceName,
        message: 'Service uninstalled successfully'
      });
      console.log(`${this.serviceName} uninstalled successfully.`);
    });

    this.service.on('alreadyuninstalled', () => {
      logger.logService('ALREADY_UNINSTALLED', {
        serviceName: this.serviceName,
        message: 'Service is already uninstalled'
      });
      console.log(`${this.serviceName} is already uninstalled.`);
    });

    // Service start/stop events
    this.service.on('start', () => {
      logger.logService('STARTED', {
        serviceName: this.serviceName,
        message: 'Service started successfully'
      });
      console.log(`${this.serviceName} started successfully.`);
    });

    this.service.on('stop', () => {
      logger.logService('STOPPED', {
        serviceName: this.serviceName,
        message: 'Service stopped successfully'
      });
      console.log(`${this.serviceName} stopped successfully.`);
    });

    // Error events
    this.service.on('error', (error) => {
      logger.error('Windows service error:', {
        serviceName: this.serviceName,
        error: error.message
      });
      console.error(`${this.serviceName} error:`, error);
    });
  }

  /**
   * Install the Windows service
   */
  install() {
    try {
      if (!this.service) {
        this.createService();
      }

      logger.logService('INSTALL_START', {
        serviceName: this.serviceName,
        message: 'Starting service installation'
      });

      this.service.install();

    } catch (error) {
      logger.error('Error installing Windows service:', error);
      throw error;
    }
  }

  /**
   * Uninstall the Windows service
   */
  uninstall() {
    try {
      if (!this.service) {
        this.createService();
      }

      logger.logService('UNINSTALL_START', {
        serviceName: this.serviceName,
        message: 'Starting service uninstallation'
      });

      this.service.uninstall();

    } catch (error) {
      logger.error('Error uninstalling Windows service:', error);
      throw error;
    }
  }

  /**
   * Start the Windows service
   */
  start() {
    try {
      if (!this.service) {
        this.createService();
      }

      logger.logService('START_REQUEST', {
        serviceName: this.serviceName,
        message: 'Requesting service start'
      });

      this.service.start();

    } catch (error) {
      logger.error('Error starting Windows service:', error);
      throw error;
    }
  }

  /**
   * Stop the Windows service
   */
  stop() {
    try {
      if (!this.service) {
        this.createService();
      }

      logger.logService('STOP_REQUEST', {
        serviceName: this.serviceName,
        message: 'Requesting service stop'
      });

      this.service.stop();

    } catch (error) {
      logger.error('Error stopping Windows service:', error);
      throw error;
    }
  }

  /**
   * Get service information
   */
  getServiceInfo() {
    return {
      name: this.serviceName,
      description: this.serviceDescription,
      scriptPath: this.scriptPath,
      exists: this.service ? this.service.exists : false
    };
  }
}

module.exports = WindowsServiceManager;
