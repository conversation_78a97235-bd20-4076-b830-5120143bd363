# Environment Configuration Guide

This project supports multiple environment configurations: Development, Staging, and Production.

## Environment Files

- `.env.development` - Development environment configuration
- `.env.staging` - Staging environment configuration  
- `.env.production` - Production environment configuration

## How It Works

The application automatically loads the appropriate environment file based on the `NODE_ENV` environment variable:

- `NODE_ENV=development` → loads `.env.development`
- `NODE_ENV=staging` → loads `.env.staging`
- `NODE_ENV=production` → loads `.env.production`
- If `NODE_ENV` is not set, it defaults to `development`

## Running Different Environments

### For Unix/Linux/Mac:
```bash
# Development (with nodemon for auto-restart)
npm run dev

# Staging (with nodemon for auto-restart)
npm run staging

# Production (without nodemon)
npm run production

# Or start without nodemon:
npm run start:dev
npm run start:staging
npm run start:production
```

### For Windows:
```bash
# Development (with nodemon for auto-restart)
npm run dev:win

# Staging (with nodemon for auto-restart)
npm run staging:win

# Production (without nodemon)
npm run production:win

# Or start without nodemon:
npm run start:dev:win
npm run start:staging:win
npm run start:production:win
```

## Setting Up Environment Files

### 1. Development Environment
The `.env.development` file is already configured for local development with localhost URLs and development database settings.

### 2. Staging Environment
Update `.env.staging` with your staging environment values:

```bash
# Database
APP_DB_HOST=your-staging-db-host.amazonaws.com
APP_DB_PASSWORD=your-staging-db-password
APP_DB_NAME=gmb_staging

# AWS S3
APP_AWS_ACCESS_KEY_ID=your-staging-aws-access-key
APP_AWS_SECRET_ACCESS_KEY=your-staging-aws-secret-key
APP_AWS_S3_BUCKET=gmb-social-assets-staging

# Social Media Apps (use staging app credentials)
FACEBOOK_CLIENT_ID=your-staging-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-staging-facebook-client-secret
FACEBOOK_REDIRECT_URI=https://your-staging-domain.com/v1/facebook/callback

# Frontend URLs
FRONTEND_URL=https://your-staging-frontend-domain.com
UI_ORIGIN=https://your-staging-frontend-domain.com
```

### 3. Production Environment
Update `.env.production` with your production environment values:

```bash
# Database
APP_DB_HOST=your-production-db-host.amazonaws.com
APP_DB_PASSWORD=your-production-db-password
APP_DB_NAME=gmb_production

# AWS S3
APP_AWS_ACCESS_KEY_ID=your-production-aws-access-key
APP_AWS_SECRET_ACCESS_KEY=your-production-aws-secret-key
APP_AWS_S3_BUCKET=gmb-social-assets-production

# Social Media Apps (use production app credentials)
FACEBOOK_CLIENT_ID=your-production-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-production-facebook-client-secret
FACEBOOK_REDIRECT_URI=https://your-production-domain.com/v1/facebook/callback

# Frontend URLs
FRONTEND_URL=https://your-production-frontend-domain.com
UI_ORIGIN=https://your-production-frontend-domain.com
```

## Environment Variables to Update

When setting up staging and production environments, make sure to update:

1. **Database credentials** - Different database for each environment
2. **AWS credentials** - Separate AWS accounts or IAM users
3. **S3 bucket names** - Different buckets for each environment
4. **Social media app credentials** - Separate apps for staging/production
5. **Domain URLs** - Update all callback URLs and frontend URLs
6. **API keys** - Use environment-specific API keys
7. **JWT secrets** - Use different secrets for each environment
8. **Log levels** - Production should use ERROR, staging/dev can use INFO

## Security Best Practices

1. **Never commit sensitive credentials** to version control
2. **Use different credentials** for each environment
3. **Rotate secrets regularly** especially in production
4. **Use strong, unique JWT secrets** for each environment
5. **Limit database access** by environment
6. **Use separate AWS accounts** or strict IAM policies

## Deployment

### Staging Deployment
```bash
# Set environment and start
NODE_ENV=staging npm start
```

### Production Deployment
```bash
# Set environment and start
NODE_ENV=production npm start
```

## Troubleshooting

1. **Environment not loading**: Check that the `.env.{environment}` file exists
2. **Wrong environment**: Verify `NODE_ENV` is set correctly
3. **Missing variables**: Check console output for which environment file is being loaded
4. **Database connection issues**: Verify database credentials for the specific environment

## Environment Verification

The application logs which environment file it's loading on startup:
```
Loading environment configuration from: .env.development
```

You can also check the current environment by visiting the root endpoint (`/`) which returns:
```json
{
  "message": "Backend is running",
  "environment": "DEVELOPMENT"
}
```
