-- Create GMB Posts Log Table for tracking post modifications
CREATE TABLE IF NOT EXISTS `gmb_posts_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL,
  `google_post_name` varchar(500) NOT NULL,
  `user_id` int(11) NOT NULL,
  `business_id` int(11) DEFAULT NULL,
  `location_id` varchar(255) DEFAULT NULL,
  `account_id` varchar(255) DEFAULT NULL,
  `action_type` enum('CREATE','UPDATE','DELETE','BULK_CREATE','BULK_UPDATE','BULK_DELETE') NOT NULL,
  `old_content` longtext DEFAULT NULL,
  `new_content` longtext DEFAULT NULL,
  `old_summary` text DEFAULT NULL,
  `new_summary` text DEFAULT NULL,
  `old_topic_type` varchar(50) DEFAULT NULL,
  `new_topic_type` varchar(50) DEFAULT NULL,
  `old_state` varchar(50) DEFAULT NULL,
  `new_state` varchar(50) DEFAULT NULL,
  `old_bulk_post_id` varchar(255) DEFAULT NULL,
  `new_bulk_post_id` varchar(255) DEFAULT NULL,
  `old_is_bulk_post` tinyint(1) DEFAULT NULL,
  `new_is_bulk_post` tinyint(1) DEFAULT NULL,
  `changes_made` json DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_google_post_name` (`google_post_name`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_gmb_posts_log_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_gmb_posts_log_business` FOREIGN KEY (`business_id`) REFERENCES `business` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add indexes for better performance
CREATE INDEX `idx_gmb_posts_log_composite` ON `gmb_posts_log` (`post_id`, `action_type`, `created_at`);
CREATE INDEX `idx_gmb_posts_log_bulk` ON `gmb_posts_log` (`old_bulk_post_id`, `new_bulk_post_id`);
