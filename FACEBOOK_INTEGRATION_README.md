# Facebook Business Integration

This document outlines the complete Facebook Business integration implementation for the aqib-gmb-social platform.

## Overview

The Facebook integration allows users to:

- Connect their Facebook Business accounts
- Select Facebook pages to manage
- Create and publish posts to Facebook pages
- Upload images with posts
- Schedule posts for later publication
- View post creation status and results

## Architecture

### Frontend Components

- **FacebookLogin**: Handles Facebook OAuth authentication
- **FacebookPageSelector**: Dropdown to select Facebook pages
- **FacebookPostForm**: Complete form for creating Facebook posts
- **FacebookService**: API service for Facebook operations

### Backend Components

- **FacebookService**: Core Facebook API integration
- **FacebookController**: Request handling and business logic
- **FacebookModels**: Database operations for Facebook data
- **FacebookRoutes**: API endpoint definitions

### Database Tables

- **facebook_oauth_tokens**: Stores user OAuth tokens
- **facebook_pages**: Stores connected Facebook pages
- **facebook_posts**: Stores created posts and metadata

## Setup Instructions

### 1. Facebook App Configuration

1. Go to [Facebook Developers](https://developers.facebook.com/apps/)
2. Create a new app with "Business" type
3. Add "Facebook Login" product
4. Configure OAuth redirect URIs:
   - Development: `http://localhost:5000/v1/facebook/callback`
   - Production: `https://yourdomain.com/v1/facebook/callback`
5. Request the following permissions in App Review:
   - `pages_manage_posts`
   - `pages_read_engagement`
   - `pages_show_list`
   - `public_profile`

### 2. Environment Configuration

Copy `.env.facebook.example` to your `.env.development` file and add:

```env
FACEBOOK_CLIENT_ID=your_facebook_app_id
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret
FACEBOOK_REDIRECT_URI=http://localhost:5000/v1/facebook/callback
FRONTEND_URL=http://localhost:3000
```

### 3. Database Setup

The database configuration is already available in `.env.development`.

**⚠️ Important:** If you encounter MySQL syntax errors, please refer to the [Facebook Database Setup Guide](FACEBOOK_DATABASE_SETUP_GUIDE.md) for multiple setup options compatible with different MySQL versions.

#### Quick Setup (Recommended)

```bash
# Test database connection
npm run test:facebook-db

# Create Facebook tables (compatible with most MySQL versions)
npm run setup:facebook-simple

# Add Facebook columns to business table
npm run add:facebook-columns
```

#### Alternative Setup Methods

If the quick setup doesn't work, see [FACEBOOK_DATABASE_SETUP_GUIDE.md](FACEBOOK_DATABASE_SETUP_GUIDE.md) for:

- Automated Node.js setup
- Manual SQL execution
- Troubleshooting for different MySQL versions
- Rollback instructions

### 4. Install Dependencies

No additional dependencies are required. The integration uses existing packages.

## Quick Start Guide

1. **Test Database Connection:**

   ```bash
   npm run test:facebook-db
   ```

2. **Setup Facebook Tables:**

   ```bash
   npm run setup:facebook
   ```

3. **Configure Facebook App:**

   - Create Facebook app at https://developers.facebook.com/
   - Add credentials to `.env.development`

4. **Start Application:**

   ```bash
   npm run devStart
   ```

5. **Test Integration:**
   - Navigate to Create Social Post
   - Click Facebook tab
   - Click "Connect Facebook"

## API Endpoints

### Authentication

- `POST /v1/facebook/authenticate` - Initiate Facebook OAuth
- `GET /v1/facebook/callback` - Handle OAuth callback

### Pages Management

- `GET /v1/facebook/pages/:userId/:businessId` - Get user's Facebook pages

### Post Management

- `POST /v1/facebook/post/:userId/:businessId` - Create Facebook post

## Usage Flow

### 1. User Authentication

1. User clicks "Connect Facebook" button
2. System generates OAuth URL and redirects to Facebook
3. User authorizes the app on Facebook
4. Facebook redirects back with authorization code
5. System exchanges code for access tokens
6. System retrieves and saves user's Facebook pages

### 2. Post Creation

1. User selects Facebook tab in create post interface
2. User selects a Facebook page from dropdown
3. User fills in post details (message, description, link)
4. User optionally uploads images
5. User optionally schedules post for later
6. System uploads images to S3 if provided
7. System creates post on Facebook via API
8. System saves post details to database
9. User sees success confirmation with Facebook URL

## Features

### Implemented Features

✅ Facebook OAuth authentication
✅ Page selection and management
✅ Text post creation
✅ Image post creation
✅ Post scheduling
✅ Database persistence
✅ Error handling and logging
✅ URL generation for created posts

### Planned Features

🔄 Video post support
🔄 Multiple image posts
🔄 Post editing capabilities
🔄 Post analytics integration
🔄 Bulk posting to multiple pages

## Error Handling

The integration includes comprehensive error handling:

- Invalid Facebook credentials
- Expired access tokens
- API rate limiting
- Network connectivity issues
- Database operation failures

## Security Considerations

- Access tokens are stored securely in the database
- All API calls are authenticated
- OAuth state parameter prevents CSRF attacks
- Sensitive data is logged appropriately

## Testing

### Manual Testing Steps

1. Configure Facebook app and environment variables
2. Start the application
3. Navigate to Create Social Post
4. Click Facebook tab
5. Click "Connect Facebook" button
6. Complete OAuth flow
7. Select a Facebook page
8. Create a test post
9. Verify post appears on Facebook

### Automated Testing

- Unit tests for service methods
- Integration tests for API endpoints
- Database operation tests

## Troubleshooting

### Common Issues

**"Facebook configuration is incomplete"**

- Verify all environment variables are set
- Check Facebook app credentials

**"Facebook page not found"**

- Ensure user has connected their Facebook account
- Verify page permissions

**"Failed to create Facebook post"**

- Check Facebook API permissions
- Verify access token validity
- Review Facebook API error response

### Debugging

Enable debug logging by setting log level to 'debug' in your environment configuration.

## Performance Considerations

- Access tokens are cached in database
- API calls are optimized for minimal requests
- Image uploads use S3 for efficient storage
- Database queries are indexed for performance

## Compliance

The integration follows Facebook's platform policies:

- Proper permission requests
- User consent for data access
- Secure token storage
- Appropriate API usage

## Support

For issues or questions regarding the Facebook integration:

1. Check the troubleshooting section
2. Review Facebook Developer documentation
3. Check application logs for detailed error messages
4. Contact the development team with specific error details
