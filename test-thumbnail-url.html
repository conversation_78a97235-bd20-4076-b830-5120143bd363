<!DOCTYPE html>
<html>
<head>
    <title>Test Thumbnail URL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-container { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        img { max-width: 300px; max-height: 300px; border: 1px solid #ddd; }
        .url-display { word-break: break-all; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>Thumbnail URL Test</h1>
    
    <div class="test-container">
        <h3>Testing Thumbnail URL from API Response:</h3>
        <div class="url-display">
            https://gmb-social-assets.s3.amazonaws.com/business-assets/57/thumbnails/0e8fac5d-3443-4992-804c-84fcfe125ff2.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA6GBMBOAGSGR5Q7DQ%2F20250609%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250609T205226Z&X-Amz-Expires=604800&X-Amz-Signature=ac521b960ec373c74d6f310a24634156dd18ebdcdd48e5039f5ea0ae63673b0f&X-Amz-SignedHeaders=host
        </div>
        
        <h4>Image Test:</h4>
        <img 
            id="thumbnailImg" 
            src="https://gmb-social-assets.s3.amazonaws.com/business-assets/57/thumbnails/0e8fac5d-3443-4992-804c-84fcfe125ff2.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA6GBMBOAGSGR5Q7DQ%2F20250609%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250609T205226Z&X-Amz-Expires=604800&X-Amz-Signature=ac521b960ec373c74d6f310a24634156dd18ebdcdd48e5039f5ea0ae63673b0f&X-Amz-SignedHeaders=host"
            alt="Thumbnail test"
            onload="showSuccess('Thumbnail loaded successfully!')"
            onerror="showError('Thumbnail failed to load')"
        />
        
        <div id="result"></div>
    </div>
    
    <div class="test-container">
        <h3>Testing Main Image URL from API Response:</h3>
        <div class="url-display">
            https://gmb-social-assets.s3.amazonaws.com/business-assets/57/5c032c82-e568-4b30-bbf0-05f2b9010963.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA6GBMBOAGSGR5Q7DQ%2F20250609%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250609T205226Z&X-Amz-Expires=604800&X-Amz-Signature=2fcbb7584e5c029fd2e695a1900088080a174e07a41d15a81458936d6323e192&X-Amz-SignedHeaders=host
        </div>
        
        <h4>Image Test:</h4>
        <img 
            id="mainImg" 
            src="https://gmb-social-assets.s3.amazonaws.com/business-assets/57/5c032c82-e568-4b30-bbf0-05f2b9010963.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA6GBMBOAGSGR5Q7DQ%2F20250609%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250609T205226Z&X-Amz-Expires=604800&X-Amz-Signature=2fcbb7584e5c029fd2e695a1900088080a174e07a41d15a81458936d6323e192&X-Amz-SignedHeaders=host"
            alt="Main image test"
            onload="showSuccess('Main image loaded successfully!', 'mainResult')"
            onerror="showError('Main image failed to load', 'mainResult')"
        />
        
        <div id="mainResult"></div>
    </div>

    <script>
        function showSuccess(message, elementId = 'result') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="test-container success">${message}</div>`;
        }
        
        function showError(message, elementId = 'result') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="test-container error">${message}</div>`;
        }
        
        // Test fetch requests
        async function testUrls() {
            const thumbnailUrl = "https://gmb-social-assets.s3.amazonaws.com/business-assets/57/thumbnails/0e8fac5d-3443-4992-804c-84fcfe125ff2.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA6GBMBOAGSGR5Q7DQ%2F20250609%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250609T205226Z&X-Amz-Expires=604800&X-Amz-Signature=ac521b960ec373c74d6f310a24634156dd18ebdcdd48e5039f5ea0ae63673b0f&X-Amz-SignedHeaders=host";
            
            const mainUrl = "https://gmb-social-assets.s3.amazonaws.com/business-assets/57/5c032c82-e568-4b30-bbf0-05f2b9010963.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA6GBMBOAGSGR5Q7DQ%2F20250609%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250609T205226Z&X-Amz-Expires=604800&X-Amz-Signature=2fcbb7584e5c029fd2e695a1900088080a174e07a41d15a81458936d6323e192&X-Amz-SignedHeaders=host";
            
            console.log('Testing thumbnail URL with fetch...');
            try {
                const response = await fetch(thumbnailUrl, { method: 'HEAD' });
                console.log('Thumbnail fetch result:', response.status, response.statusText);
            } catch (error) {
                console.error('Thumbnail fetch error:', error);
            }
            
            console.log('Testing main image URL with fetch...');
            try {
                const response = await fetch(mainUrl, { method: 'HEAD' });
                console.log('Main image fetch result:', response.status, response.statusText);
            } catch (error) {
                console.error('Main image fetch error:', error);
            }
        }
        
        // Run tests when page loads
        window.onload = function() {
            testUrls();
        };
    </script>
</body>
</html>
