# Facebook Integration Database Setup Guide

This guide provides multiple methods to set up the Facebook integration database tables, compatible with different MySQL versions.

## Database Configuration

The database configuration is already available in `.env.development`:

```env
APP_DB_HOST=aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com
APP_DB_USER=admin
APP_DB_PASSWORD=lAverOpERiaN
APP_DB_NAME=gmb
```

## Setup Methods

### Method 1: Automated Setup (Recommended)

This method uses Node.js scripts to safely create tables and add columns:

```bash
# Step 1: Test database connection
npm run test:facebook-db

# Step 2: Create Facebook tables
npm run setup:facebook

# Step 3: Add Facebook columns to business table
npm run add:facebook-columns
```

### Method 2: Simple SQL Script (MySQL 5.7+ Compatible)

If you encounter syntax errors with the automated setup, use the simple SQL script:

```bash
# Option A: Using npm script (requires mysql client)
npm run setup:facebook-simple

# Option B: Manual execution
mysql -h aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com -u admin -p gmb < database/migrations/facebook_integration_simple.sql
```

Then add the business table columns:

```bash
npm run add:facebook-columns
```

### Method 3: Manual SQL Execution

Connect to MySQL and execute the commands manually:

```sql
-- Connect to database
USE gmb;

-- Create Facebook OAuth tokens table
CREATE TABLE IF NOT EXISTS facebook_oauth_tokens (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  business_id INT NOT NULL,
  access_token TEXT NOT NULL,
  refresh_token TEXT,
  expires_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  status_id TINYINT DEFAULT 1,
  UNIQUE KEY unique_user_business (user_id, business_id),
  INDEX idx_user_id (user_id),
  INDEX idx_business_id (business_id),
  INDEX idx_status (status_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create Facebook pages table
CREATE TABLE IF NOT EXISTS facebook_pages (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  business_id INT NOT NULL,
  page_id VARCHAR(255) NOT NULL,
  page_name VARCHAR(255) NOT NULL,
  page_access_token TEXT NOT NULL,
  page_category VARCHAR(255),
  page_picture_url TEXT,
  is_active TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_user_business_page (user_id, business_id, page_id),
  INDEX idx_user_id (user_id),
  INDEX idx_business_id (business_id),
  INDEX idx_page_id (page_id),
  INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create Facebook posts table
CREATE TABLE IF NOT EXISTS facebook_posts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  business_id INT NOT NULL,
  page_id VARCHAR(255) NOT NULL,
  facebook_post_id VARCHAR(255),
  bulk_post_id VARCHAR(36) NULL,
  is_bulk_post BOOLEAN DEFAULT FALSE,
  post_content JSON NOT NULL,
  post_response JSON,
  message TEXT,
  description TEXT,
  link VARCHAR(2048),
  published BOOLEAN DEFAULT TRUE,
  scheduled_publish_time TIMESTAMP NULL,
  status VARCHAR(50) DEFAULT 'published',
  facebook_url TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_business_id (business_id),
  INDEX idx_page_id (page_id),
  INDEX idx_bulk_post_id (bulk_post_id),
  INDEX idx_facebook_post_id (facebook_post_id),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add columns to business table (check if they exist first)
-- For facebook_sync_status
SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_schema = 'gmb' 
                   AND table_name = 'gmb_businesses_master' 
                   AND column_name = 'facebook_sync_status');

SET @sql = IF(@col_exists = 0, 
              'ALTER TABLE gmb_businesses_master ADD COLUMN facebook_sync_status TINYINT(1) DEFAULT 0', 
              'SELECT "Column facebook_sync_status already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- For facebook_page_id
SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_schema = 'gmb' 
                   AND table_name = 'gmb_businesses_master' 
                   AND column_name = 'facebook_page_id');

SET @sql = IF(@col_exists = 0, 
              'ALTER TABLE gmb_businesses_master ADD COLUMN facebook_page_id VARCHAR(255) DEFAULT NULL', 
              'SELECT "Column facebook_page_id already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add index
SET @index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                     WHERE table_schema = 'gmb' 
                     AND table_name = 'gmb_businesses_master' 
                     AND index_name = 'idx_facebook_page_id');

SET @sql = IF(@index_exists = 0, 
              'ALTER TABLE gmb_businesses_master ADD INDEX idx_facebook_page_id (facebook_page_id)', 
              'SELECT "Index idx_facebook_page_id already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
```

## Verification

After running any of the setup methods, verify the installation:

```sql
-- Check tables exist
SHOW TABLES LIKE 'facebook_%';

-- Check table structures
DESCRIBE facebook_oauth_tokens;
DESCRIBE facebook_pages;
DESCRIBE facebook_posts;

-- Check business table columns
SELECT column_name, data_type, column_default 
FROM information_schema.columns 
WHERE table_schema = 'gmb' 
AND table_name = 'gmb_businesses_master' 
AND column_name IN ('facebook_sync_status', 'facebook_page_id');
```

## Troubleshooting

### Error: "You have an error in your SQL syntax"

This usually occurs with older MySQL versions that don't support `IF NOT EXISTS` for columns. Use **Method 2** or **Method 3** instead.

### Error: "Column already exists"

This is normal if you've run the setup multiple times. The scripts are designed to handle this gracefully.

### Error: "Access denied"

Check your database credentials in `.env.development` and ensure the user has the necessary privileges.

### Error: "Database connection refused"

Verify that:
- The database server is running
- The host and port are correct
- Network connectivity is available

## Available NPM Scripts

```bash
# Test database connection
npm run test:facebook-db

# Full automated setup (may have compatibility issues)
npm run setup:facebook

# Simple table creation (compatible with older MySQL)
npm run setup:facebook-simple

# Add Facebook columns to business table
npm run add:facebook-columns
```

## Next Steps

After successful database setup:

1. Configure Facebook app credentials in `.env.development`
2. Start the application: `npm run devStart`
3. Test the Facebook integration in the Create Social Post screen

## Rollback

If you need to remove the Facebook integration:

```sql
DROP TABLE IF EXISTS facebook_posts;
DROP TABLE IF EXISTS facebook_pages;
DROP TABLE IF EXISTS facebook_oauth_tokens;

ALTER TABLE gmb_businesses_master 
DROP COLUMN IF EXISTS facebook_sync_status,
DROP COLUMN IF EXISTS facebook_page_id,
DROP INDEX IF EXISTS idx_facebook_page_id;
```

Note: Use `DROP COLUMN` without `IF EXISTS` for older MySQL versions.
