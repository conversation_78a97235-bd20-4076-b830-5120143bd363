import { Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import { Action } from "redux";
import { IReportsFilterData } from "../../components/reportsFilter/reportsFilter.component";
import { IPerformanceDateRange } from "../../components/performanceDateFilter/performanceDateFilter.component";

export interface IReviewsReportResponse {
  success: boolean;
  message: string;
  data: {
    reviews: any[];
    aggregated: {
      ratingsVsMonth: Record<string, Record<number, number>>;
      reviewsVsReplies: Record<string, { reviews: number; replies: number }>;
      ratingDistribution: Record<number, number>;
      reviewVolume: Record<string, number>;
      responseRate: { total: number; replied: number };
      avgResponseTime: number[];
    };
  };
}

export interface IPerformanceReportResponse {
  success: boolean;
  message: string;
  data: {
    performance: {
      currentPeriod: {
        totalReviews: number;
        totalReplies: number;
        responseRate: number;
        avgRating: number;
        avgResponseTime: number;
        ratingDistribution: Record<number, number>;
      };
      comparisonPeriod: {
        totalReviews: number;
        totalReplies: number;
        responseRate: number;
        avgRating: number;
        avgResponseTime: number;
        ratingDistribution: Record<number, number>;
      };
      trends: {
        reviewsChange: number;
        repliesChange: number;
        responseRateChange: number;
        avgRatingChange: number;
        avgResponseTimeChange: number;
      };
      charts: {
        dailyVolume: Record<string, { reviews: number; replies: number }>;
        responseRateTrend: Record<
          string,
          { total: number; replied: number; responseRate: number }
        >;
        ratingTrends: Record<
          string,
          {
            total: number;
            sum: number;
            avgRating: number;
            ratings: Record<number, number>;
          }
        >;
        responseTimeAnalysis: Record<string, number>;
        weeklyComparison: {
          current: Record<
            string,
            { total: number; replied: number; responseRate: number }
          >;
          comparison: Record<
            string,
            { total: number; replied: number; responseRate: number }
          >;
        };
      };
    };
    dateRange: { fromDate: string; toDate: string };
    totalRecords: number;
  };
}

export interface IPerformanceFilterData {
  businessId: string;
  accountId: string;
  locationId: string;
  fromDate: string;
  toDate: string;
}

export default class ReportsService {
  _httpHelperService: HttpHelperService;

  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  async getReviewsReport(
    filters: IReportsFilterData
  ): Promise<IReviewsReportResponse> {
    const requestData = {
      businessId: filters.businessId !== "0" ? filters.businessId : null,
      accountId: filters.accountId !== "0" ? filters.accountId : null,
      locationId: filters.locationId !== "0" ? filters.locationId : null,
      startDate: filters.startDate || null,
      endDate: filters.endDate || null,
    };

    console.log("Reports service - sending request:", requestData);
    const response = await this._httpHelperService.post(
      "reports/reviews",
      requestData
    );
    console.log("Reports service - received response:", response);
    return response;
  }

  async exportReviewsReport(filters: IReportsFilterData): Promise<any> {
    const requestData = {
      businessId: filters.businessId !== "0" ? filters.businessId : null,
      accountId: filters.accountId !== "0" ? filters.accountId : null,
      locationId: filters.locationId !== "0" ? filters.locationId : null,
      startDate: filters.startDate || null,
      endDate: filters.endDate || null,
    };

    return await this._httpHelperService.post(
      "reports/reviews/export",
      requestData
    );
  }

  async getPerformanceReport(
    filters: IPerformanceFilterData
  ): Promise<IPerformanceReportResponse> {
    const requestData = {
      businessId: filters.businessId !== "0" ? filters.businessId : null,
      accountId: filters.accountId !== "0" ? filters.accountId : null,
      locationId: filters.locationId !== "0" ? filters.locationId : null,
      fromDate: filters.fromDate || null,
      toDate: filters.toDate || null,
    };

    console.log("Performance reports service - sending request:", requestData);
    const response = await this._httpHelperService.post(
      "reports/performance",
      requestData
    );
    console.log("Performance reports service - received response:", response);
    return response;
  }

  async exportPerformanceReport(filters: IPerformanceFilterData): Promise<any> {
    const requestData = {
      businessId: filters.businessId !== "0" ? filters.businessId : null,
      accountId: filters.accountId !== "0" ? filters.accountId : null,
      locationId: filters.locationId !== "0" ? filters.locationId : null,
      fromDate: filters.fromDate || null,
      toDate: filters.toDate || null,
    };

    return await this._httpHelperService.post(
      "reports/performance/export",
      requestData
    );
  }
}
