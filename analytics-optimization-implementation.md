# Analytics Optimization Implementation

## Overview

This implementation optimizes the Google My Business analytics data retrieval by storing analytics data in a local database instead of making multiple Google API calls for each request. This significantly reduces API usage, improves performance, and reduces billing costs.

## Architecture Changes

### Database Schema

Three new tables have been created:

1. **`location_analytics_daily`** - Stores daily metrics for each location
2. **`location_analytics_sync_log`** - Tracks sync operations and their status
3. **`location_analytics_config`** - Manages sync configuration for each location

### API Endpoints

#### Updated Endpoints:

- **`/performance/performance-locationMetrics`** - Now checks database first, falls back to Google API
- **`/performance/search-keywords`** - Unchanged (still uses Google API)

#### New Endpoints:

- **`/performance/multi-location-analytics`** - Optimized endpoint for multiple locations

### Frontend Changes

- **LocationMetricsService** - Added `getMultiLocationAnalytics()` method
- **GMB Reports Screen** - Updated to use the new multi-location endpoint

## Implementation Details

### Database Models

The `LocationAnalytics` model provides methods for:

- Batch inserting/updating daily metrics
- Retrieving analytics data for single or multiple locations
- Managing sync configuration and logging
- Formatting data to match Google API response structure

### API Controller Updates

The `refreshLocationMetrics` controller now:

1. First attempts to retrieve data from the database
2. Falls back to Google API if no data is found
3. Returns data with a `source` indicator (`database` or `google_api`)

### Analytics Sync Service

The analytics synchronization is handled by the separate `aqib-gmb-sync-service` project:

- Runs daily at 1:00 AM to sync analytics data
- Fetches data from Google API and stores in database
- Handles batch processing and error management
- Provides comprehensive logging and monitoring

### Frontend Optimization

The analytics screen now:

1. Uses a single API call for multiple locations
2. Receives pre-aggregated data from the database
3. Shows informative messages when data needs synchronization

## Benefits

### Performance Improvements

- **Reduced API Calls**: From N calls (one per location) to 1 call for all locations
- **Faster Response Times**: Database queries are much faster than Google API calls
- **Better User Experience**: Immediate data loading from local database

### Cost Reduction

- **Lower Google API Usage**: Significant reduction in API quota consumption
- **Reduced Billing**: Fewer API calls mean lower costs

### Reliability

- **Offline Capability**: Analytics data available even if Google API is down
- **Data Persistence**: Historical data is preserved locally
- **Fallback Mechanism**: Automatic fallback to Google API when needed

## Migration Steps

### 1. Database Migration

```sql
-- Execute the migration script
mysql -u username -p database_name < database/migrations/analytics_tables.sql
```

### 2. Test Implementation

```bash
# Run the test script
node scripts/test-analytics-implementation.js
```

### 3. Sync Service Setup

The analytics sync functionality is implemented in the separate `aqib-gmb-sync-service` project:

- Analytics sync service runs daily at 1:00 AM
- Fetches analytics data from this API project
- Stores data in the new database tables
- Handles errors and retry logic
- See the `aqib-gmb-sync-service` project for sync service configuration

## API Usage Examples

### Single Location Analytics (Backward Compatible)

```javascript
const response = await locationMetricsService.getLocationMetrics(
  { startDate: "2024-01-01", endDate: "2024-01-31" },
  { "x-gmb-account-id": accountId, "x-gmb-location-id": locationId }
);
```

### Multi-Location Analytics (New Optimized Endpoint)

```javascript
const response = await locationMetricsService.getMultiLocationAnalytics({
  locationIds: ["location1", "location2", "location3"],
  startDate: "2024-01-01",
  endDate: "2024-01-31",
});
```

## Data Flow

### Before Optimization

```
Frontend → API → Google API (N calls) → Response
```

### After Optimization

```
Frontend → API → Database → Response (if data exists)
Frontend → API → Google API → Response (fallback)
```

### With Sync Service

```
Sync Service → Google API → Database (daily sync)
Frontend → API → Database → Response (fast)
```

## Monitoring and Maintenance

### Key Metrics to Monitor

- Database query performance
- Sync service success rate
- API fallback frequency
- Data freshness

### Maintenance Tasks

- Regular cleanup of old analytics data (configurable retention)
- Monitor sync service logs
- Verify data accuracy between database and Google API

## Configuration

### Environment Variables

- `TIMEZONE` - Timezone for sync scheduling (default: UTC)

### Database Configuration

- Retention period: 365 days (configurable per location)
- Sync frequency: Daily (configurable per location)

## Troubleshooting

### Common Issues

1. **No Data in Database**

   - Check if sync service is running
   - Verify OAuth tokens are valid
   - Check sync logs for errors

2. **Performance Issues**

   - Monitor database indexes
   - Check query execution plans
   - Consider data archiving for old records

3. **Data Inconsistencies**
   - Compare database data with Google API responses
   - Check sync service logs for partial failures
   - Verify date range calculations

## Future Enhancements

1. **Real-time Sync**: Implement webhook-based real-time updates
2. **Data Validation**: Add data quality checks and validation rules
3. **Analytics Dashboard**: Create admin dashboard for sync monitoring
4. **Caching Layer**: Add Redis caching for frequently accessed data
5. **Data Export**: Implement bulk data export functionality
