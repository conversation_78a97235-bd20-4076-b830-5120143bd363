-- Create instagram_tokens table
CREATE TABLE IF NOT EXISTS instagram_tokens (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  access_token TEXT NOT NULL,
  page_data JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_user (user_id),
  INDEX idx_user_id (user_id)
);

-- Add Instagram sync status columns to businesses table if they don't exist
ALTER TABLE gmb_businesses_master 
ADD COLUMN IF NOT EXISTS instagramSyncStatus TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS instagramAccountId VARCHAR(255) DEFAULT NULL;

-- Add index for Instagram account ID
ALTER TABLE gmb_businesses_master 
ADD INDEX IF NOT EXISTS idx_instagram_account_id (instagramAccountId);
