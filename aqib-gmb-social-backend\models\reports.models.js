const pool = require("../config/db");
const RoleType = require("../constants/dbConstants");

module.exports = class Reports {
  constructor() {}

  // Get user role and validate access
  static async getUserRole(userId) {
    try {
      const userData = await pool.query("SELECT * FROM users WHERE id = ?", [
        userId,
      ]);

      if (!userData || userData.length === 0) {
        throw new Error("User not found");
      }

      return userData[0].roleId;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  // Fetch reviews data with filters and role-based access control
  static async fetchReviewsData(userId, filters) {
    try {
      const { businessId, accountId, locationId, startDate, endDate } = filters;
      const userRole = await this.getUserRole(userId);

      let baseQuery = `
        SELECT 
          gr.*,
          gl.gmbLocationName,
          gl.gmbAccountId,
          ga.accountName,
          gb.businessName,
          CASE gr.StarRating   
            WHEN 'ONE' THEN 1    
            WHEN 'TWO' THEN 2    
            WHEN 'THREE' THEN 3    
            WHEN 'FOUR' THEN 4    
            WHEN 'FIVE' THEN 5 
          END AS StarRatingInt,
          CASE 
            WHEN gr.reviewReplyComment IS NOT NULL AND gr.reviewReplyComment != '' THEN 1 
            ELSE 0 
          END AS hasReply,
          DATE_FORMAT(gr.createTime, '%Y-%m') as monthYear,
          DATE_FORMAT(gr.createTime, '%Y-%m-%d') as reviewDate
        FROM gmb_reviews gr
        JOIN gmb_locations gl ON gl.gmbLocationId = gr.locationId
        JOIN gmb_accounts ga ON gl.gmbAccountId = ga.accountId
        JOIN gmb_businesses_master gb ON ga.businessId = gb.id
      `;

      let whereConditions = [];
      let queryParams = [];

      // Role-based access control
      if (userRole === RoleType.Admin) {
        // Admin can see all data
      } else if (userRole === RoleType.Manager) {
        baseQuery += ` JOIN user_business ub ON ub.businessId = gb.id `;
        whereConditions.push("ub.userId = ?");
        queryParams.push(userId);
      } else {
        baseQuery += ` JOIN users_gmb_locations ul ON ul.gmbLocationId = gl.gmbLocationId `;
        whereConditions.push("ul.userId = ?");
        queryParams.push(userId);
      }

      // Apply filters
      if (businessId && businessId !== "0") {
        whereConditions.push("gb.id = ?");
        queryParams.push(businessId);
      }

      if (accountId && accountId !== "0") {
        whereConditions.push("ga.accountId = ?");
        queryParams.push(accountId);
      }

      if (locationId && locationId !== "0") {
        whereConditions.push("gr.locationId = ?");
        queryParams.push(locationId);
      }

      if (startDate) {
        whereConditions.push("DATE(gr.createTime) >= ?");
        queryParams.push(startDate);
      }

      if (endDate) {
        whereConditions.push("DATE(gr.createTime) <= ?");
        queryParams.push(endDate);
      }

      if (whereConditions.length > 0) {
        baseQuery += ` WHERE ${whereConditions.join(" AND ")}`;
      }

      baseQuery += ` ORDER BY gr.createTime DESC`;

      console.log("Final query:", baseQuery);
      console.log("Query params:", queryParams);

      const reviews = await pool.query(baseQuery, queryParams);
      console.log("Query results count:", reviews.length);

      return reviews;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  // Generate aggregated data for charts
  static generateAggregatedData(reviews) {
    const aggregated = {
      ratingsVsMonth: {},
      reviewsVsReplies: {},
      ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
      reviewVolume: {},
      responseRate: { total: 0, replied: 0 },
      avgResponseTime: [],
    };

    reviews.forEach((review) => {
      const monthYear = review.monthYear;
      const rating = review.StarRatingInt;
      const hasReply = review.hasReply;

      // Ratings vs Month
      if (!aggregated.ratingsVsMonth[monthYear]) {
        aggregated.ratingsVsMonth[monthYear] = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
      }
      aggregated.ratingsVsMonth[monthYear][rating]++;

      // Reviews vs Replies by month
      if (!aggregated.reviewsVsReplies[monthYear]) {
        aggregated.reviewsVsReplies[monthYear] = { reviews: 0, replies: 0 };
      }
      aggregated.reviewsVsReplies[monthYear].reviews++;
      if (hasReply) {
        aggregated.reviewsVsReplies[monthYear].replies++;
      }

      // Rating Distribution
      aggregated.ratingDistribution[rating]++;

      // Review Volume
      if (!aggregated.reviewVolume[monthYear]) {
        aggregated.reviewVolume[monthYear] = 0;
      }
      aggregated.reviewVolume[monthYear]++;

      // Response Rate
      aggregated.responseRate.total++;
      if (hasReply) {
        aggregated.responseRate.replied++;
      }

      // Response Time (if reply exists)
      if (hasReply && review.reviewReplyUpdateTime) {
        const reviewTime = new Date(review.createTime);
        const replyTime = new Date(review.reviewReplyUpdateTime);
        const responseTimeHours = (replyTime - reviewTime) / (1000 * 60 * 60);
        aggregated.avgResponseTime.push(responseTimeHours);
      }
    });

    return aggregated;
  }

  // Fetch reviews data for export (can be extended with different formatting)
  static async fetchReviewsForExport(userId, filters) {
    try {
      // Use the same query as fetchReviewsData but can be modified for export needs
      return await this.fetchReviewsData(userId, filters);
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  // Fetch performance data for reviews and replies with comparison periods
  static async fetchPerformanceData(userId, filters) {
    try {
      const { businessId, accountId, locationId, fromDate, toDate } = filters;
      const userRole = await this.getUserRole(userId);

      // Calculate comparison period (same duration before the selected period)
      const startDate = new Date(fromDate);
      const endDate = new Date(toDate);
      const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
      const comparisonStartDate = new Date(startDate);
      comparisonStartDate.setDate(startDate.getDate() - daysDiff);
      const comparisonEndDate = new Date(startDate);
      comparisonEndDate.setDate(startDate.getDate() - 1);

      let baseQuery = `
        SELECT
          gr.*,
          gl.gmbLocationName,
          gl.gmbAccountId,
          ga.accountName,
          gb.businessName,
          CASE gr.StarRating
            WHEN 'ONE' THEN 1
            WHEN 'TWO' THEN 2
            WHEN 'THREE' THEN 3
            WHEN 'FOUR' THEN 4
            WHEN 'FIVE' THEN 5
          END AS StarRatingInt,
          CASE
            WHEN gr.reviewReplyComment IS NOT NULL AND gr.reviewReplyComment != '' THEN 1
            ELSE 0
          END AS hasReply,
          DATE_FORMAT(gr.createTime, '%Y-%m-%d') as reviewDate,
          DATE_FORMAT(gr.createTime, '%Y-%m') as monthYear,
          CASE
            WHEN DATE(gr.createTime) BETWEEN ? AND ? THEN 'current'
            WHEN DATE(gr.createTime) BETWEEN ? AND ? THEN 'comparison'
            ELSE 'other'
          END AS period,
          CASE
            WHEN gr.reviewReplyComment IS NOT NULL AND gr.reviewReplyComment != ''
            THEN TIMESTAMPDIFF(HOUR, gr.createTime, gr.reviewReplyUpdateTime)
            ELSE NULL
          END AS responseTimeHours
        FROM gmb_reviews gr
        JOIN gmb_locations gl ON gl.gmbLocationId = gr.locationId
        JOIN gmb_accounts ga ON gl.gmbAccountId = ga.accountId
        JOIN gmb_businesses_master gb ON ga.businessId = gb.id
      `;

      let whereConditions = [];
      let queryParams = [
        fromDate,
        toDate,
        comparisonStartDate.toISOString().split("T")[0],
        comparisonEndDate.toISOString().split("T")[0],
      ];

      // Role-based access control
      if (userRole === RoleType.Admin) {
        // Admin can see all data
      } else if (userRole === RoleType.Manager) {
        whereConditions.push(`
          EXISTS (
            SELECT 1 FROM user_business ub
            WHERE ub.businessId = gb.id AND ub.userId = ?
          )
        `);
        queryParams.push(userId);
      } else {
        whereConditions.push(`
          EXISTS (
            SELECT 1 FROM users_gmb_locations ul
            WHERE ul.gmbLocationId = gl.gmbLocationId AND ul.userId = ?
          )
        `);
        queryParams.push(userId);
      }

      // Add filter conditions
      if (businessId && businessId !== "0") {
        whereConditions.push("gb.id = ?");
        queryParams.push(businessId);
      }

      if (accountId && accountId !== "0") {
        whereConditions.push("ga.accountId = ?");
        queryParams.push(accountId);
      }

      if (locationId && locationId !== "0") {
        whereConditions.push("gr.locationId = ?");
        queryParams.push(locationId);
      }

      // Add date range condition for both periods
      whereConditions.push(`
        (DATE(gr.createTime) BETWEEN ? AND ?) OR
        (DATE(gr.createTime) BETWEEN ? AND ?)
      `);
      queryParams.push(
        fromDate,
        toDate,
        comparisonStartDate.toISOString().split("T")[0],
        comparisonEndDate.toISOString().split("T")[0]
      );

      if (whereConditions.length > 0) {
        baseQuery += ` WHERE ${whereConditions.join(" AND ")}`;
      }

      baseQuery += ` ORDER BY gr.createTime DESC`;

      console.log("Performance query:", baseQuery);
      console.log("Query params:", queryParams);

      const reviews = await pool.query(baseQuery, queryParams);
      console.log("Performance query results count:", reviews.length);

      return reviews;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  // Generate performance analytics data with trends and comparisons
  static generatePerformanceData(reviews) {
    const currentPeriod = reviews.filter((r) => r.period === "current");
    const comparisonPeriod = reviews.filter((r) => r.period === "comparison");

    const performance = {
      currentPeriod: this.calculatePeriodMetrics(currentPeriod),
      comparisonPeriod: this.calculatePeriodMetrics(comparisonPeriod),
      trends: {},
      charts: {},
    };

    // Calculate percentage changes
    performance.trends = this.calculateTrends(
      performance.currentPeriod,
      performance.comparisonPeriod
    );

    // Generate chart data
    performance.charts = {
      dailyVolume: this.generateDailyVolumeChart(currentPeriod),
      responseRateTrend: this.generateResponseRateTrend(currentPeriod),
      ratingTrends: this.generateRatingTrends(currentPeriod),
      responseTimeAnalysis: this.generateResponseTimeAnalysis(currentPeriod),
      weeklyComparison: this.generateWeeklyComparison(
        currentPeriod,
        comparisonPeriod
      ),
    };

    return performance;
  }

  // Calculate metrics for a specific period
  static calculatePeriodMetrics(reviews) {
    const metrics = {
      totalReviews: reviews.length,
      totalReplies: reviews.filter((r) => r.hasReply === 1).length,
      responseRate: 0,
      avgRating: 0,
      avgResponseTime: 0,
      ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
    };

    if (reviews.length > 0) {
      metrics.responseRate =
        (metrics.totalReplies / metrics.totalReviews) * 100;

      const totalRating = reviews.reduce((sum, r) => sum + r.StarRatingInt, 0);
      metrics.avgRating = totalRating / reviews.length;

      // Calculate rating distribution
      reviews.forEach((r) => {
        metrics.ratingDistribution[r.StarRatingInt]++;
      });

      // Calculate average response time
      const responseTimes = reviews
        .filter((r) => r.hasReply === 1 && r.responseTimeHours !== null)
        .map((r) => r.responseTimeHours);

      if (responseTimes.length > 0) {
        metrics.avgResponseTime =
          responseTimes.reduce((sum, time) => sum + time, 0) /
          responseTimes.length;
      }
    }

    return metrics;
  }

  // Calculate trends and percentage changes
  static calculateTrends(current, comparison) {
    const calculateChange = (currentVal, comparisonVal) => {
      if (comparisonVal === 0) return currentVal > 0 ? 100 : 0;
      return ((currentVal - comparisonVal) / comparisonVal) * 100;
    };

    return {
      reviewsChange: calculateChange(
        current.totalReviews,
        comparison.totalReviews
      ),
      repliesChange: calculateChange(
        current.totalReplies,
        comparison.totalReplies
      ),
      responseRateChange: calculateChange(
        current.responseRate,
        comparison.responseRate
      ),
      avgRatingChange: calculateChange(current.avgRating, comparison.avgRating),
      avgResponseTimeChange: calculateChange(
        current.avgResponseTime,
        comparison.avgResponseTime
      ),
    };
  }

  // Generate daily volume chart data
  static generateDailyVolumeChart(reviews) {
    const dailyData = {};

    reviews.forEach((review) => {
      const date = review.reviewDate;
      if (!dailyData[date]) {
        dailyData[date] = { reviews: 0, replies: 0 };
      }
      dailyData[date].reviews++;
      if (review.hasReply === 1) {
        dailyData[date].replies++;
      }
    });

    return dailyData;
  }

  // Generate response rate trend data
  static generateResponseRateTrend(reviews) {
    const weeklyData = {};

    reviews.forEach((review) => {
      const date = new Date(review.reviewDate);
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      const weekKey = weekStart.toISOString().split("T")[0];

      if (!weeklyData[weekKey]) {
        weeklyData[weekKey] = { total: 0, replied: 0 };
      }
      weeklyData[weekKey].total++;
      if (review.hasReply === 1) {
        weeklyData[weekKey].replied++;
      }
    });

    // Convert to percentage
    Object.keys(weeklyData).forEach((week) => {
      const data = weeklyData[week];
      weeklyData[week].responseRate =
        data.total > 0 ? (data.replied / data.total) * 100 : 0;
    });

    return weeklyData;
  }

  // Generate rating trends data
  static generateRatingTrends(reviews) {
    const weeklyRatings = {};

    reviews.forEach((review) => {
      const date = new Date(review.reviewDate);
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      const weekKey = weekStart.toISOString().split("T")[0];

      if (!weeklyRatings[weekKey]) {
        weeklyRatings[weekKey] = {
          total: 0,
          sum: 0,
          ratings: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
        };
      }
      weeklyRatings[weekKey].total++;
      weeklyRatings[weekKey].sum += review.StarRatingInt;
      weeklyRatings[weekKey].ratings[review.StarRatingInt]++;
    });

    // Calculate average ratings
    Object.keys(weeklyRatings).forEach((week) => {
      const data = weeklyRatings[week];
      weeklyRatings[week].avgRating =
        data.total > 0 ? data.sum / data.total : 0;
    });

    return weeklyRatings;
  }

  // Generate response time analysis
  static generateResponseTimeAnalysis(reviews) {
    const repliedReviews = reviews.filter(
      (r) => r.hasReply === 1 && r.responseTimeHours !== null
    );

    const timeRanges = {
      "0-1h": 0,
      "1-6h": 0,
      "6-24h": 0,
      "1-3d": 0,
      "3d+": 0,
    };

    repliedReviews.forEach((review) => {
      const hours = review.responseTimeHours;
      if (hours <= 1) timeRanges["0-1h"]++;
      else if (hours <= 6) timeRanges["1-6h"]++;
      else if (hours <= 24) timeRanges["6-24h"]++;
      else if (hours <= 72) timeRanges["1-3d"]++;
      else timeRanges["3d+"]++;
    });

    return timeRanges;
  }

  // Generate weekly comparison data
  static generateWeeklyComparison(current, comparison) {
    const currentWeekly = this.generateResponseRateTrend(current);
    const comparisonWeekly = this.generateResponseRateTrend(comparison);

    return {
      current: currentWeekly,
      comparison: comparisonWeekly,
    };
  }
};
