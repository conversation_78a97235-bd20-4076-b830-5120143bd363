// Test the S3 URL fix
console.log("Testing S3 URL generation with correct region...");

// Simulate environment variables
const APP_AWS_S3_BUCKET = "gmb-social-assets";
const APP_AWS_S3_BUCKET_REGION = "us-east-1";

// Test URL generation
const testS3Key = "business-assets/45/test-image.jpg";
const bucketRegion = APP_AWS_S3_BUCKET_REGION;

let testUrl;
if (bucketRegion === "us-east-1") {
  testUrl = `https://${APP_AWS_S3_BUCKET}.s3.amazonaws.com/${testS3Key}`;
} else {
  testUrl = `https://${APP_AWS_S3_BUCKET}.s3.${bucketRegion}.amazonaws.com/${testS3Key}`;
}

console.log("\nBefore fix (causing PermanentRedirect):");
console.log(
  `https://${APP_AWS_S3_BUCKET}.s3.ap-south-1.amazonaws.com/${testS3Key}`
);

console.log("\nAfter fix (should work):");
console.log(testUrl);

console.log("\nThis should resolve the PermanentRedirect error!");
console.log("The bucket is in us-east-1, so we use s3.amazonaws.com endpoint.");
