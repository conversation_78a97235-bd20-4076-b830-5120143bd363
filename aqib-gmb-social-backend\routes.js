const express = require("express");
const router = express.Router();

//Routes
const authRoutes = require("./routes/auth");
const userRoutes = require("./routes/user");
const gmbRoutes = require("./routes/gmb");
const businessRoutes = require("./routes/business");
const locationsRoutes = require("./routes/locations");
const reviewRoutes = require("./routes/reviews");
const reviewSettingsRoutes = require("./routes/reviewSettings");
const QandARoutes = require("./routes/QandA");
const roleRoutes = require("./routes/role");
const performanceRoutes = require("./routes/locationMetrics");
const postRoutes = require("./routes/post");

const localFalconRoutes = require("./routes/localFalcon");
const manageAssetsRoutes = require("./routes/manageAssets");
const instagramRoutes = require("./routes/instagram");
const facebookRoutes = require("./routes/facebook");
const linkedinRoutes = require("./routes/linkedin");
const twitterRoutes = require("./routes/twitter");
const reportsRoutes = require("./routes/reports");
const businessProfileRoutes = require("./routes/businessProfile.routes");
const googlePlacesRoutes = require("./routes/googlePlaces");

router.get("/", async (req, res) => {
  res.send({ app_version: process.env.APP_VER_PREFIX });
});

router.use("/auth", authRoutes);
router.use("/user", userRoutes);
router.use("/gmb", gmbRoutes);
router.use("/business", businessRoutes);
router.use("/locations", locationsRoutes);
router.use("/gmb-reviews", reviewRoutes);
router.use("/review-settings", reviewSettingsRoutes);
router.use("/gmb-QandA", QandARoutes);
router.use("/role", roleRoutes);
router.use("/performance", performanceRoutes);
router.use("/post", postRoutes);

router.use("/local-falcon", localFalconRoutes);
router.use("/manage-assets", manageAssetsRoutes);
router.use("/instagram", instagramRoutes);
router.use("/facebook", facebookRoutes);
router.use("/linkedin", linkedinRoutes);
router.use("/twitter", twitterRoutes);
router.use("/reports", reportsRoutes);
router.use("/business-profile", businessProfileRoutes);
router.use("/google-places", googlePlacesRoutes);

module.exports = router;
